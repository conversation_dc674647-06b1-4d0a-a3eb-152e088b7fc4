/**
 * == AI 圖片按鈕處理模組 ==
 * 🔘 專門處理圖片相關按鈕功能的函數
 * 🔧 v1.6.0 - Reply API 優先修復版
 *
 * 📁 本檔案職責：
 * - 故事參數按鈕 (handleStoryParamsButton)
 * - 故事接龍按鈕 (handleStoryContinueButton)
 * - 分享故事按鈕 (handleShareStoryButton)
 * - 圖片尺寸按鈕 (handleResizeButton)
 *
 * 🎯 v1.6.0 修復重點：
 * - ✅ 所有按鈕函數都優先使用 Reply API（不占配額）
 * - ✅ 完善的 replyToken 檢查邏輯
 * - ✅ 統一的錯誤處理和日誌記錄
 * - ✅ 配額節省效果統計
 *
 * 🔗 依賴檔案：
 * - AIHandlers_ImageCore.gs (handleStoryContinuation)
 * - AIHandlers_ImagePush.gs (pushTextMessageV143)
 * - AIHandlers_ImageUtils.gs (shortenUrl)
 */

// ===== 🔘 按鈕處理函數 =====

/**
 * 📋 處理故事參數按鈕（整合所有生成參數）
 */
function handleStoryParamsButton(storyParamsData, targetInfo) {
  try {
    console.log(`📋 處理故事參數按鈕`);
    console.log(`📋 接收到的數據:`, JSON.stringify(storyParamsData));

    // 檢查數據完整性
    if (!storyParamsData) {
      throw new Error('故事參數數據為空');
    }

    // 獲取配置
    const config = getConfig();

    // 構建詳細參數文字
    const paramsText = `📋 圖片生成參數詳情

🎲 SEED：${storyParamsData.seed}
🤖 模型版本：${storyParamsData.model}
${storyParamsData.processingTime && storyParamsData.processingTime > 0 ? `⏱️ 生成時間：${storyParamsData.processingTime.toFixed(1)} 秒` : ''}

📝 原始提示詞：
${storyParamsData.userOriginalPrompt}

${storyParamsData.aiProcessedPrompt && storyParamsData.aiProcessedPrompt !== storyParamsData.userOriginalPrompt && storyParamsData.aiProcessedPrompt !== 'undefined' ? `🔄 AI 處理後提示詞：\n${storyParamsData.aiProcessedPrompt}\n\n` : ''}${storyParamsData.imageStory && storyParamsData.imageStory !== '生成失敗' && storyParamsData.imageStory.trim() ? `📖 配圖故事：\n${storyParamsData.imageStory}\n\n` : ''}---
${config.footer}`;

    // 🎯 修復：優先使用 Reply API（不占配額）
    if (targetInfo.replyToken) {
      console.log(`✅ 使用 Reply API 發送故事參數（不占配額）`);
      replyMessage(targetInfo.replyToken, paramsText);
      return { success: true, method: 'reply_api', target: targetInfo.sourceType };
    } else {
      console.log(`📤 無 replyToken，使用 Push API 發送故事參數`);
      return pushTextMessageV143(targetInfo, paramsText);
    }

  } catch (error) {
    console.error('處理故事參數按鈕失敗:', error);
    return {
      success: false,
      error: error.message
    };
  }
}

/**
 * 🎬 處理故事接龍按鈕
 * 🔧 v1.6.0 修復：確保 handleStoryContinuation 使用 Reply API 優先邏輯
 */
function handleStoryContinueButton(storyContinueData, targetInfo) {
  try {
    console.log(`🎬 [v1.6.0] 處理故事接龍按鈕`);
    console.log(`🎬 [v1.6.0] 目標信息: ${targetInfo.sourceType}(${targetInfo.userId}), replyToken: ${targetInfo.replyToken ? '有' : '無'}`);

    // 🚀 v1.6.0 配合：調用修復後的 handleStoryContinuation（已優先使用 Reply API）
    const result = handleStoryContinuation(
      storyContinueData.seed,
      storyContinueData.prompt,
      targetInfo
    );

    // 記錄配額節省情況
    if (result && result.quotaSaved) {
      console.log(`✅ [v1.6.0] 故事接龍按鈕配額節省成功`);
    }

    return result;

  } catch (error) {
    console.error('❌ [v1.6.0] 處理故事接龍按鈕失敗:', error);

    // 🔧 v1.6.0 錯誤處理：優先使用 Reply API
    if (targetInfo.replyToken) {
      console.log(`✅ [v1.6.0] 故事接龍錯誤使用 Reply API（不占配額）`);
      const errorText = `❌ 故事接龍功能暫時無法使用

🔧 錯誤原因：${error.message}

💡 請稍後再試，或使用文字指令：
「繼續故事 [您的描述]」`;

      replyMessage(targetInfo.replyToken, errorText);
      return { success: true, method: 'reply_api', target: targetInfo.sourceType, quotaSaved: true };
    } else {
      console.log(`📤 [v1.6.0] 故事接龍錯誤使用 Push API（占配額）`);
      return {
        success: false,
        error: error.message,
        method: 'push_api_fallback'
      };
    }
  }
}

/**
 * 📤 處理分享故事按鈕
 * 🔧 v1.6.6 - 修正：使用已有故事，防重複點擊
 * 🔧 v1.6.7 - 修正：支援兩條調用鏈的故事數據
 *
 * 📋 調用鏈支援：
 * 🔵 被動發圖鏈：imageStory通常有值
 * 🟡 偽主動發圖鏈：imageStory可能為null，需從story_params查找
 */
function handleShareStoryButton(shareStoryData, targetInfo) {
  try {
    console.log(`📤 處理分享故事按鈕`);
    console.log(`📖 收到的故事數據: ${JSON.stringify(shareStoryData)}`);

    // 🔍 調用鏈診斷
    const chainType = shareStoryData.imageStory ? '🔵 被動發圖鏈' : '🟡 偽主動發圖鏈';
    console.log(`🔍 調用鏈類型: ${chainType}`);

    // 🔒 防重複點擊檢查
    const clickKey = `share_story_${shareStoryData.seed || Date.now()}_${targetInfo.userId}`;
    const lastClick = PropertiesService.getScriptProperties().getProperty(clickKey);
    const now = Date.now();

    if (lastClick && (now - parseInt(lastClick)) < 10000) { // 10秒內防重複
      console.log(`🔒 防重複點擊：忽略重複請求`);
      return { success: true, method: 'duplicate_ignored', target: targetInfo.sourceType };
    }

    // 記錄點擊時間
    PropertiesService.getScriptProperties().setProperty(clickKey, now.toString());

    // 🎯 優先使用故事參數按鈕中的故事（那裡有完整的故事）
    let shareContent = '';

    // 先檢查是否有完整的故事數據
    if (shareStoryData.imageStory && shareStoryData.imageStory !== '生成失敗' && shareStoryData.imageStory.trim()) {
      shareContent = shareStoryData.imageStory;
      console.log(`✅ 使用現有故事，長度: ${shareContent.length}`);
    } else {
      // 🔍 嘗試從故事參數數據中獲取故事
      console.log(`🔍 分享數據中無有效故事，檢查是否能從其他地方獲取...`);

      // 檢查是否有seed，可以查找對應的故事參數數據
      if (shareStoryData.seed) {
        try {
          const sheet = SpreadsheetApp.getActiveSpreadsheet().getSheetByName('PostbackCache');
          if (sheet && sheet.getLastRow() > 1) {
            const data = sheet.getDataRange().getValues();

            // 查找相同seed的story_params數據
            for (let i = data.length - 1; i >= 1; i--) {
              if (data[i][1] === 'story_params' && data[i][2] == shareStoryData.seed) {
                try {
                  const storyParamsData = JSON.parse(data[i][5] || '{}');
                  if (storyParamsData.imageStory && storyParamsData.imageStory !== '生成失敗') {
                    shareContent = storyParamsData.imageStory;
                    console.log(`✅ 從故事參數數據中找到故事，長度: ${shareContent.length}`);
                    break;
                  }
                } catch (parseError) {
                  console.warn(`解析故事參數數據失敗:`, parseError);
                }
              }
            }
          }
        } catch (searchError) {
          console.warn(`搜索故事參數數據失敗:`, searchError);
        }
      }

      // 如果還是沒有找到，使用預設內容
      if (!shareContent) {
        shareContent = '✨ 精美AI生成圖片';
        console.log(`⚠️ 未找到有效故事，使用預設內容`);
      }
    }

    // 構建最終內容：只要故事和連結
    const imageUrl = shortenUrl(shareStoryData.imageUrl) || shareStoryData.imageUrl;
    const finalContent = `${shareContent}

${imageUrl}`;

    console.log(`📤 最終分享內容長度: ${finalContent.length}`);

    // 發送純文字
    if (targetInfo.replyToken) {
      replyMessage(targetInfo.replyToken, finalContent);
      return { success: true, method: 'reply_api', target: targetInfo.sourceType };
    } else {
      return pushTextMessageV143(targetInfo, finalContent);
    }

  } catch (error) {
    console.error('處理分享故事按鈕失敗:', error);

    // 🔄 v1.6.4 備用：使用現有的pushTextMessageV143（已包含Reply API優先邏輯）
    try {
      const fallbackContent = `📤 分享故事內容

${shareStoryData.imageStory ? `${shareStoryData.imageStory}` : ''}

${shortenUrl(shareStoryData.imageUrl) || shareStoryData.imageUrl}

---
${config.footer}

💡 請手動複製上方內容進行分享`;

      // 🎯 v1.6.4 備用方案：重用現有函數（已包含完整的Reply API優先邏輯）
      console.log(`🔄 使用備用方案：pushTextMessageV143（已包含Reply API優先邏輯）`);
      return pushTextMessageV143(targetInfo, fallbackContent);

    } catch (fallbackError) {
      console.error('備用分享方案也失敗:', fallbackError);
      return {
        success: false,
        error: error.message
      };
    }
  }
}

/**
 * 📐 處理圖片尺寸按鈕
 */
function handleResizeButton(resizeData, aspectRatio, targetInfo) {
  try {
    console.log(`📐 處理圖片尺寸按鈕: ${aspectRatio}`);

    // 修改提示詞以包含尺寸要求
    let resizedPrompt = resizeData.originalPrompt;

    if (aspectRatio === '16:9') {
      resizedPrompt += ', 橫向構圖, 16:9 比例';
    } else if (aspectRatio === '9:16') {
      resizedPrompt += ', 直向構圖, 9:16 比例';
    }

    // 發送確認訊息
    const confirmMessage = `📐 正在生成 ${aspectRatio} 比例的圖片...

📝 新的描述：${resizedPrompt}
🎲 使用相同 SEED：${resizeData.seed}`;

    // 🎯 修復：優先使用 Reply API 發送確認訊息（不占配額）
    let pushResult;
    if (targetInfo.replyToken) {
      console.log(`✅ 使用 Reply API 發送尺寸確認訊息（不占配額）`);
      replyMessage(targetInfo.replyToken, confirmMessage);
      pushResult = { success: true, method: 'reply_api', target: targetInfo.sourceType };
    } else {
      console.log(`📤 無 replyToken，使用 Push API 發送尺寸確認訊息`);
      pushResult = pushTextMessageV143(targetInfo, confirmMessage);
    }

    if (pushResult.success) {
      // 觸發新的圖片生成（異步）
      setTimeout(() => {
        executeAsyncImageGenerationV143(resizedPrompt, `調整尺寸為 ${aspectRatio}`, resizedPrompt, targetInfo);
      }, 1000);
    }

    return pushResult;

  } catch (error) {
    console.error('處理圖片尺寸按鈕失敗:', error);
    return {
      success: false,
      error: error.message
    };
  }
}



// ===== 📋 模組說明 v1.6.5 =====
// 🎯 v1.6.5 重大修復：分享故事和複製功能優化
//
// 📈 修復效果：
// - ✅ 修正"生成失敗"顯示問題，智能處理故事內容
// - ✅ 替換LINE分享URI為postback，避免二維碼問題
// - ✅ 新增複製到剪貼簿功能，直接提供文字內容
// - ✅ 保持Reply API優先策略，節省配額
// - ✅ 優化用戶體驗，支援threads分享
//
// 🔧 修復的函數：
// 1. handleStoryParamsButton - 故事參數按鈕
// 2. handleStoryContinueButton - 故事接龍按鈕
// 3. handleShareStoryButton - 分享故事按鈕（v1.6.5 重大修復）
// 4. handleResizeButton - 圖片尺寸按鈕
// 5. handleCopyTextButton - 複製到剪貼簿按鈕（v1.6.5 新增）
//
// 🎉 配合其他模組修復，整體系統配額節省效果顯著！
//
// 📁 相關模組：
// - AIHandlers_ImageCore.gs - 核心圖片生成功能
// - AIHandlers_ImagePush.gs - 推送相關功能
// - AIHandlers_ImageUtils.gs - 工具函數
