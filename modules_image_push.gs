/**
 * == AI 圖片推送處理模組 ==
 * 📤 專門處理圖片結果推送和錯誤處理的函數
 * 🔧 v1.6.0 - Reply API 優先修復版：優先使用 Reply API，大幅節省 Push API 配額
 *
 * 📁 本檔案職責：
 * - 智能推送圖片結果 (pushImageResultV143)
 * - 推送錯誤訊息 (pushImageErrorV143, pushImageSystemErrorV143)
 * - 通用推送函數 (pushTextMessageV143, pushFlexMessageV143)
 * - 錯誤處理機制 (buildPushFailureMessage, buildFlexFailureMessage, buildTextFailureMessage)
 * - 簡化版推送 (pushSimplifiedImageResult)
 *
 * 🎯 v1.6.0 修復重點：
 * - ✅ 所有推送函數都優先檢查 replyToken
 * - ✅ 優先使用 Reply API（不占配額）
 * - ✅ 只有在沒有 replyToken 時才使用 Push API
 * - ✅ 統一錯誤處理和回退機制
 * - ✅ 詳細的配額節省記錄
 *
 * 🔗 依賴檔案：
 * - Utils.gs (getConfig, replyMessage)
 * - PostbackDataManager.gs (storePostbackData)
 * - AIHandlers_ImageUtils.gs (shortenUrl)
 */

// ===== 📤 圖片推送函數 =====

/**
 * 🔧 v1.6.0 - 智能推送圖片結果（Reply API 優先版）
 * 🎯 修復：優先使用 Reply API，大幅節省 Push API 配額
 */
function pushImageResultV143(targetInfo, imageResult, userOriginalPrompt, userOriginalPromptEnglish, aiProcessedPrompt, processingTime, imageStory = null) {
  try {
    const config = getConfig();

    if (!config.lineChannelAccessToken) {
      throw new Error('LINE Channel Access Token 未設定');
    }

    console.log(`🚀 [v1.6.0] 智能推送圖片結果（Reply API 優先）`);
    console.log(`🎯 目標分析: sourceType=${targetInfo.sourceType}, userId=${targetInfo.userId}, replyToken=${targetInfo.replyToken ? '有' : '無'}`);

    // 📋 準備圖片 URL（直接信任 Cloudinary/Google Drive 的 URL）
    let imageUrl;
    if (imageResult.cloudinaryUrl) {
      imageUrl = imageResult.cloudinaryUrl;
      console.log(`✅ 使用 Cloudinary 圖片: ${imageUrl}`);
    } else if (imageResult.driveUrl) {
      imageUrl = imageResult.driveUrl;
      console.log(`📁 使用 Google Drive 圖片: ${imageUrl}`);
    } else {
      throw new Error('無法獲取有效的圖片 URL');
    }

    // 🎲 準備顯示資訊
    const seedValue = imageResult.seed || Math.floor(Math.random() * 1000000);
    const modelName = imageResult.modelUsed || '未知模型';

    console.log(`🎲 SEED: ${seedValue}, 🤖 模型: ${modelName}, ⏱️ 耗時: ${processingTime}秒`);

    // 🔧 v1.6.0 新增：優先檢查 replyToken（核心修復）
    if (targetInfo.replyToken) {
      console.log(`✅ [v1.6.0] 檢測到 replyToken，使用 Reply API（不占配額）`);
      
      try {
        // 構建簡化的圖片結果訊息（Reply API 適用）
        const resultText = `🎨 圖片生成完成！

📝 您的描述：${userOriginalPrompt}
🌐 英文提示：${userOriginalPromptEnglish}
⏱️ 處理時間：${processingTime.toFixed(1)} 秒
🎲 SEED：${seedValue}

🖼️ 圖片連結：${imageUrl}`;

        // 如果有故事，添加故事內容
        const finalText = imageStory && imageStory.trim() 
          ? `${resultText}

📖 配圖故事：
${imageStory}

💡 您可以：
• 點擊連結查看完整圖片
• 使用相同 SEED 重新生成
• 嘗試其他描述` 
          : `${resultText}

💡 您可以：
• 點擊連結查看完整圖片
• 使用相同 SEED 重新生成
• 嘗試其他描述`;

        // 使用 Reply API 發送
        replyMessage(targetInfo.replyToken, finalText);
        
        console.log(`✅ [v1.6.0] Reply API 發送成功，節省 Push API 配額`);
        return {
          success: true,
          method: 'reply_api',
          target: `${targetInfo.sourceType}(${targetInfo.userId})`,
          imageUrl: imageUrl,
          seed: seedValue,
          quotaSaved: true
        };
        
      } catch (replyError) {
        console.error(`❌ [v1.6.0] Reply API 發送失敗:`, replyError);
        // 降級到 Push API，繼續執行下面的邏輯
        console.log(`🔄 [v1.6.0] 降級到 Push API...`);
      }
    } else {
      console.log(`📤 [v1.6.0] 無 replyToken，使用 Push API（占配額）`);
    }

    // 🎯 Push API 邏輯（當沒有 replyToken 或 Reply API 失敗時）
    // 根據來源類型確定推送目標
    let pushTarget;
    let targetDescription;

    if (targetInfo.sourceType === 'group' && targetInfo.groupId) {
      pushTarget = targetInfo.groupId;
      targetDescription = `群組(${targetInfo.groupId})`;
    } else if (targetInfo.sourceType === 'room' && targetInfo.roomId) {
      pushTarget = targetInfo.roomId;
      targetDescription = `聊天室(${targetInfo.roomId})`;
    } else {
      pushTarget = targetInfo.userId;
      targetDescription = `個人(${targetInfo.userId})`;
    }

    console.log(`📤 [v1.6.0] Push API 推送目標: ${targetDescription}`);

    // 🔧 v1.5.1 修復：準備新按鈕的數據
    // 故事參數按鈕數據（整合所有參數）
    const storyParamsData = {
      seed: seedValue,
      model: modelName,
      userOriginalPrompt: userOriginalPrompt,  // 用戶原始輸入
      aiProcessedPrompt: aiProcessedPrompt,    // AI處理後的提示詞
      processingTime: processingTime,
      imageUrl: imageUrl,
      imageStory: imageStory,
      timestamp: new Date().toISOString()
    };
    const storyParamsDataId = storePostbackData('story_params', seedValue, modelName, userOriginalPrompt, storyParamsData);

    // 故事接龍按鈕數據
    const storyContinueData = {
      seed: seedValue,
      prompt: userOriginalPrompt,  // 使用用戶原始輸入
      targetInfo: targetInfo
    };
    const storyContinueDataId = storePostbackData('story_continue', seedValue, modelName, userOriginalPrompt, storyContinueData);

    // 分享故事按鈕數據
    const shareStoryData = {
      imageUrl: imageUrl,
      imageStory: imageStory,
      prompt: userOriginalPrompt,  // 使用用戶原始輸入
      seed: seedValue
    };
    const shareStoryDataId = storePostbackData('share_story', seedValue, modelName, userOriginalPrompt, shareStoryData);

    // 圖片尺寸按鈕數據
    const resizeData = {
      originalPrompt: userOriginalPrompt,  // 使用用戶原始輸入
      seed: seedValue,
      targetInfo: targetInfo
    };
    const resize16_9DataId = storePostbackData('resize_16_9', seedValue, modelName, userOriginalPrompt, resizeData);
    const resize9_16DataId = storePostbackData('resize_9_16', seedValue, modelName, userOriginalPrompt, resizeData);

    // 🌟 構建 v1.6.0 Flex Message（保持完整功能）
    const flexMessage = {
      type: "flex",
      altText: `🎨 AI 圖片生成完成 (SEED: ${seedValue}) - 耗時 ${processingTime.toFixed(1)} 秒`,
      contents: {
        type: "bubble",
        hero: {
          type: "image",
          url: imageUrl,
          size: "full",
          aspectRatio: "1:1",
          aspectMode: "cover",
          action: {
            type: "uri",
            uri: imageUrl
          }
        },
        body: {
          type: "box",
          layout: "vertical",
          spacing: "md",
          paddingAll: "20px",
          contents: [
            {
              type: "text",
              text: userOriginalPrompt,
              size: "md",
              weight: "bold",
              color: "#333333",
              margin: "none",
              wrap: true
            },
            {
              type: "text",
              text: userOriginalPromptEnglish,
              size: "sm",
              color: "#666666",
              margin: "xs",
              wrap: true
            }
          ]
        },
        footer: {
          type: "box",
          layout: "vertical",
          spacing: "sm",
          paddingAll: "20px",
          contents: [
            // 🔘 第一排按鈕：故事參數 + 故事接龍
            {
              type: "box",
              layout: "horizontal",
              spacing: "sm",
              contents: [
                {
                  type: "button",
                  action: {
                    type: "postback",
                    label: "📋 故事參數",
                    data: `action=show_story_params&id=${storyParamsDataId}`
                  },
                  style: "primary",
                  color: "#3498db",
                  height: "sm"
                },
                {
                  type: "button",
                  action: {
                    type: "postback",
                    label: "🎬 故事接龍",
                    data: `action=story_continue&id=${storyContinueDataId}`
                  },
                  style: "primary",
                  color: "#9b59b6",
                  height: "sm"
                }
              ]
            },
            // 🔘 第二排按鈕：分享故事 + 圖片尺寸
            {
              type: "box",
              layout: "horizontal",
              spacing: "sm",
              margin: "sm",
              contents: [
                {
                  type: "button",
                  action: {
                    type: "postback",
                    label: "📤 分享故事",
                    data: `action=share_story&id=${shareStoryDataId}`
                  },
                  style: "primary",
                  color: "#27ae60",
                  height: "sm"
                },
                {
                  type: "button",
                  action: {
                    type: "postback",
                    label: "📐 圖片尺寸",
                    data: `action=resize_image&16_9=${resize16_9DataId}&9_16=${resize9_16DataId}`
                  },
                  style: "primary",
                  color: "#f39c12",
                  height: "sm"
                }
              ]
            },
            // 🆕 v1.6.7：底部文字 + SEED 顯示
            {
              type: "box",
              layout: "horizontal",
              margin: "md",
              contents: [
                {
                  type: "text",
                  text: config.footer,
                  size: "xs",
                  color: "#999999",
                  flex: 1
                },
                {
                  type: "text",
                  text: `${seedValue}`,
                  size: "xs",
                  color: "#999999",
                  align: "end"
                }
              ]
            }
          ]
        }
      }
    };

    // 📤 執行 Push API 推送
    const pushData = {
      to: pushTarget,
      messages: [flexMessage]
    };

    const response = UrlFetchApp.fetch('https://api.line.me/v2/bot/message/push', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${config.lineChannelAccessToken}`
      },
      payload: JSON.stringify(pushData)
    });

    if (response.getResponseCode() === 200) {
      console.log(`✅ [v1.6.0] Push API 推送成功到 ${targetDescription}`);
      return {
        success: true,
        method: 'push_api',
        target: targetDescription,
        pushTarget: pushTarget,
        imageUrl: imageUrl,
        seed: seedValue,
        quotaUsed: true
      };
    } else {
      const errorText = response.getContentText();
      console.error(`❌ [v1.6.0] Push API 推送失敗到 ${targetDescription}:`, errorText);

      // 🚨 嘗試簡化版本作為最後手段
      console.log(`🔄 [v1.6.0] 嘗試發送簡化版圖片結果...`);
      
      try {
        const fallbackResult = pushSimplifiedImageResult(targetInfo, imageResult, userOriginalPrompt, processingTime, errorText);
        
        if (fallbackResult.success) {
          console.log(`✅ [v1.6.0] 簡化版圖片結果發送成功`);
          return {
            success: true,
            method: 'push_api_simplified',
            target: targetDescription,
            pushTarget: pushTarget,
            imageUrl: imageUrl,
            seed: seedValue,
            fallbackUsed: true,
            originalError: errorText,
            quotaUsed: true
          };
        }
      } catch (fallbackError) {
        console.error(`❌ [v1.6.0] 簡化版發送過程出錯:`, fallbackError);
      }
      
      return {
        success: false,
        error: errorText,
        target: targetDescription,
        pushTarget: pushTarget
      };
    }

  } catch (error) {
    console.error('❌ [v1.6.0] pushImageResultV143 執行失敗:', error);
    return {
      success: false,
      error: error.message,
      target: 'unknown'
    };
  }
}

/**
 * 🔧 v1.6.0 - 推送圖片生成錯誤訊息（Reply API 優先版）
 * 🎯 修復：優先使用 Reply API，大幅節省 Push API 配額
 */
function pushImageErrorV143(targetInfo, errorMessage, imagePrompt, processingTime) {
  try {
    const errorText = `❌ 圖片生成失敗

📝 您的描述：${imagePrompt}
⏱️ 處理時間：${processingTime.toFixed(1)} 秒
🚨 錯誤原因：${errorMessage}

💡 建議：
• 嘗試簡化描述
• 避免敏感內容
• 稍後再試`;

    // 🔧 v1.6.0 修復：優先使用 Reply API
    if (targetInfo.replyToken) {
      console.log(`✅ [v1.6.0] 圖片錯誤訊息使用 Reply API（不占配額）`);
      replyMessage(targetInfo.replyToken, errorText);
      return { 
        success: true, 
        method: 'reply_api',
        target: `${targetInfo.sourceType}(${targetInfo.userId})`,
        quotaSaved: true 
      };
    } else {
      console.log(`📤 [v1.6.0] 圖片錯誤訊息使用 Push API（占配額）`);
      return pushTextMessageV143(targetInfo, errorText);
    }
  } catch (error) {
    console.error('❌ [v1.6.0] 推送圖片錯誤訊息失敗:', error);
    return { success: false, error: error.message };
  }
}

/**
 * 🔧 v1.6.0 - 推送系統錯誤訊息（Reply API 優先版）
 * 🎯 修復：優先使用 Reply API，大幅節省 Push API 配額
 */
function pushImageSystemErrorV143(targetInfo, systemError, imagePrompt) {
  try {
    const systemErrorText = `🚨 系統錯誤 - 圖片生成過程中斷

📝 您的描述：${imagePrompt}
⏰ 錯誤時間：${new Date().toLocaleString('zh-TW')}
🔧 系統訊息：${systemError}

💡 請稍後再試，或聯繫管理員`;

    // 🔧 v1.6.0 修復：優先使用 Reply API
    if (targetInfo.replyToken) {
      console.log(`✅ [v1.6.0] 系統錯誤訊息使用 Reply API（不占配額）`);
      replyMessage(targetInfo.replyToken, systemErrorText);
      return { 
        success: true, 
        method: 'reply_api',
        target: `${targetInfo.sourceType}(${targetInfo.userId})`,
        quotaSaved: true 
      };
    } else {
      console.log(`📤 [v1.6.0] 系統錯誤訊息使用 Push API（占配額）`);
      return pushTextMessageV143(targetInfo, systemErrorText);
    }
  } catch (error) {
    console.error('❌ [v1.6.0] 推送系統錯誤訊息失敗:', error);
    return { success: false, error: error.message };
  }
}

/**
 * 🔧 v1.6.0 - 通用文字訊息推送（Reply API 優先版）
 * 🎯 核心修復：優先使用 Reply API，大幅節省 Push API 配額
 */
function pushTextMessageV143(targetInfo, text) {
  try {
    const config = getConfig();

    if (!config.lineChannelAccessToken) {
      throw new Error('LINE Channel Access Token 未設定');
    }

    // 🔧 v1.6.0 核心修復：優先檢查 replyToken
    if (targetInfo.replyToken) {
      console.log(`✅ [v1.6.0] 文字訊息使用 Reply API（不占配額）`);
      
      try {
        replyMessage(targetInfo.replyToken, text);
        console.log(`✅ [v1.6.0] Reply API 文字訊息發送成功，節省 Push API 配額`);
        return {
          success: true,
          method: 'reply_api',
          target: `${targetInfo.sourceType}(${targetInfo.userId})`,
          quotaSaved: true
        };
      } catch (replyError) {
        console.error(`❌ [v1.6.0] Reply API 文字訊息發送失敗:`, replyError);
        // 降級到 Push API，繼續執行下面的邏輯
        console.log(`🔄 [v1.6.0] 文字訊息降級到 Push API...`);
      }
    } else {
      console.log(`📤 [v1.6.0] 無 replyToken，文字訊息使用 Push API（占配額）`);
    }

    // 🎯 Push API 邏輯（當沒有 replyToken 或 Reply API 失敗時）
    // 確定推送目標
    let pushTarget;
    let targetDescription;

    if (targetInfo.sourceType === 'group' && targetInfo.groupId) {
      pushTarget = targetInfo.groupId;
      targetDescription = `群組(${targetInfo.groupId})`;
    } else if (targetInfo.sourceType === 'room' && targetInfo.roomId) {
      pushTarget = targetInfo.roomId;
      targetDescription = `聊天室(${targetInfo.roomId})`;
    } else {
      pushTarget = targetInfo.userId;
      targetDescription = `個人(${targetInfo.userId})`;
    }

    console.log(`📤 [v1.6.0] Push API 文字訊息推送到: ${targetDescription}`);

    const pushData = {
      to: pushTarget,
      messages: [{
        type: 'text',
        text: text
      }]
    };

    const response = UrlFetchApp.fetch('https://api.line.me/v2/bot/message/push', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${config.lineChannelAccessToken}`
      },
      payload: JSON.stringify(pushData)
    });

    if (response.getResponseCode() === 200) {
      console.log(`✅ [v1.6.0] Push API 文字訊息推送成功到 ${targetDescription}`);
      return {
        success: true,
        method: 'push_api',
        target: targetDescription,
        pushTarget: pushTarget,
        quotaUsed: true
      };
    } else {
      const errorText = response.getContentText();
      console.error(`❌ [v1.6.0] Push API 文字訊息推送失敗到 ${targetDescription}:`, errorText);

      return {
        success: false,
        error: errorText,
        target: targetDescription
      };
    }

  } catch (error) {
    console.error('❌ [v1.6.0] pushMessage執行失敗:', error);
    return {
      success: false,
      error: error.message,
      target: 'unknown'
    };
  }
}

/**
 * 🔧 v1.6.0 - 通用 Flex Message 推送（Reply API 優先版）
 * 🎯 修復：優先使用 Reply API，大幅節省 Push API 配額
 */
function pushFlexMessageV143(targetInfo, flexMessage) {
  try {
    const config = getConfig();

    if (!config.lineChannelAccessToken) {
      throw new Error('LINE Channel Access Token 未設定');
    }

    // 🔧 v1.6.0 修復：優先檢查 replyToken
    if (targetInfo.replyToken) {
      console.log(`✅ [v1.6.0] Flex Message 使用 Reply API（不占配額）`);
      
      try {
        const replyData = {
          replyToken: targetInfo.replyToken,
          messages: [flexMessage]
        };

        const response = UrlFetchApp.fetch('https://api.line.me/v2/bot/message/reply', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${config.lineChannelAccessToken}`
          },
          payload: JSON.stringify(replyData)
        });

        if (response.getResponseCode() === 200) {
          console.log(`✅ [v1.6.0] Reply API Flex Message 發送成功，節省 Push API 配額`);
          return {
            success: true,
            method: 'reply_api',
            target: `${targetInfo.sourceType}(${targetInfo.userId})`,
            quotaSaved: true
          };
        } else {
          console.error(`❌ [v1.6.0] Reply API Flex Message 發送失敗:`, response.getContentText());
          // 降級到 Push API，繼續執行下面的邏輯
          console.log(`🔄 [v1.6.0] Flex Message 降級到 Push API...`);
        }
      } catch (replyError) {
        console.error(`❌ [v1.6.0] Reply API Flex Message 執行失敗:`, replyError);
        // 降級到 Push API，繼續執行下面的邏輯
        console.log(`🔄 [v1.6.0] Flex Message 降級到 Push API...`);
      }
    } else {
      console.log(`📤 [v1.6.0] 無 replyToken，Flex Message 使用 Push API（占配額）`);
    }

    // 🎯 Push API 邏輯（當沒有 replyToken 或 Reply API 失敗時）
    // 確定推送目標
    let pushTarget;
    let targetDescription;

    if (targetInfo.sourceType === 'group' && targetInfo.groupId) {
      pushTarget = targetInfo.groupId;
      targetDescription = `群組(${targetInfo.groupId})`;
    } else if (targetInfo.sourceType === 'room' && targetInfo.roomId) {
      pushTarget = targetInfo.roomId;
      targetDescription = `聊天室(${targetInfo.roomId})`;
    } else {
      pushTarget = targetInfo.userId;
      targetDescription = `個人(${targetInfo.userId})`;
    }

    console.log(`📤 [v1.6.0] Push API Flex Message 推送到: ${targetDescription}`);

    const pushData = {
      to: pushTarget,
      messages: [flexMessage]
    };

    const response = UrlFetchApp.fetch('https://api.line.me/v2/bot/message/push', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${config.lineChannelAccessToken}`
      },
      payload: JSON.stringify(pushData)
    });

    if (response.getResponseCode() === 200) {
      console.log(`✅ [v1.6.0] Push API Flex Message 推送成功到 ${targetDescription}`);
      return {
        success: true,
        method: 'push_api',
        target: targetDescription,
        pushTarget: pushTarget,
        quotaUsed: true
      };
    } else {
      const errorText = response.getContentText();
      console.error(`❌ [v1.6.0] Push API Flex Message 推送失敗到 ${targetDescription}:`, errorText);

      return {
        success: false,
        error: errorText,
        target: targetDescription
      };
    }

  } catch (error) {
    console.error('❌ [v1.6.0] pushFlexMessageV143 執行失敗:', error);
    return { success: false, error: error.message };
  }
}

/**
 * 🆕 v1.5.2 - 推送簡化版圖片結果（當完整版失敗時的備用方案）
 * 移除複雜的按鈕和動態內容，只保留核心信息
 */
function pushSimplifiedImageResult(targetInfo, imageResult, userOriginalPrompt, processingTime, originalError) {
  try {
    const config = getConfig();

    if (!config.lineChannelAccessToken) {
      throw new Error('LINE Channel Access Token 未設定');
    }

    // 🔧 v1.6.0 修復：優先檢查 replyToken（簡化版也要節省配額）
    if (targetInfo.replyToken) {
      console.log(`✅ [v1.6.0] 簡化版圖片結果使用 Reply API（不占配額）`);
      
      try {
        const imageUrl = imageResult.cloudinaryUrl || imageResult.driveUrl;
        const seedValue = imageResult.seed || Math.floor(Math.random() * 1000000);
        
        const simplifiedText = `🎨 圖片生成完成（簡化版）

📝 您的描述：${userOriginalPrompt.length > 100 ? userOriginalPrompt.substring(0, 100) + '...' : userOriginalPrompt}
🎲 SEED：${seedValue}
⏱️ 耗時：${processingTime.toFixed(1)} 秒

🖼️ 圖片連結：${imageUrl}

⚠️ 由於系統負載，顯示簡化版本
💡 圖片品質不受影響，請點擊連結查看`;

        replyMessage(targetInfo.replyToken, simplifiedText);
        console.log(`✅ [v1.6.0] 簡化版圖片結果 Reply API 發送成功，節省 Push API 配額`);
        return { 
          success: true, 
          method: 'reply_api_simplified',
          quotaSaved: true 
        };
      } catch (replyError) {
        console.error(`❌ [v1.6.0] 簡化版 Reply API 發送失敗:`, replyError);
        // 降級到 Push API，繼續執行下面的邏輯
        console.log(`🔄 [v1.6.0] 簡化版降級到 Push API...`);
      }
    } else {
      console.log(`📤 [v1.6.0] 無 replyToken，簡化版使用 Push API（占配額）`);
    }

    // 🎯 Push API 邏輯（原有的簡化版推送邏輯）
    // 確定推送目標
    let pushTarget;
    if (targetInfo.sourceType === 'group' && targetInfo.groupId) {
      pushTarget = targetInfo.groupId;
    } else if (targetInfo.sourceType === 'room' && targetInfo.roomId) {
      pushTarget = targetInfo.roomId;
    } else {
      pushTarget = targetInfo.userId;
    }

    // 準備圖片 URL
    let imageUrl;
    if (imageResult.cloudinaryUrl) {
      imageUrl = imageResult.cloudinaryUrl;
    } else if (imageResult.driveUrl) {
      imageUrl = imageResult.driveUrl;
    } else {
      throw new Error('無法獲取有效的圖片 URL');
    }

    const seedValue = imageResult.seed || Math.floor(Math.random() * 1000000);

    // 🎨 構建簡化版 Flex Message - 移除所有動態內容和複雜按鈕
    const simplifiedFlexMessage = {
      type: "flex",
      altText: `🎨 AI 圖片生成完成 (SEED: ${seedValue})`,
      contents: {
        type: "bubble",
        hero: {
          type: "image",
          url: imageUrl,
          size: "full",
          aspectRatio: "1:1",
          aspectMode: "cover",
          action: {
            type: "uri",
            uri: imageUrl
          }
        },
        body: {
          type: "box",
          layout: "vertical",
          spacing: "md",
          paddingAll: "20px",
          contents: [
            {
              type: "text",
              text: userOriginalPrompt.length > 80 ? userOriginalPrompt.substring(0, 80) + '...' : userOriginalPrompt,  // 限制長度
              size: "md",
              weight: "bold",
              color: "#333333",
              margin: "none",
              wrap: true
            },
            {
              type: "text",
              text: `🎲 SEED: ${seedValue} | ⏱️ ${processingTime.toFixed(1)}秒`,
              size: "sm",
              color: "#666666",
              margin: "sm",
              wrap: true
            }
          ]
        },
        footer: {
          type: "box",
          layout: "vertical",
          spacing: "sm",
          paddingAll: "20px",
          contents: [
            {
              type: "button",
              action: {
                type: "uri",
                label: "🖼️ 查看完整圖片",
                uri: imageUrl
              },
              style: "primary",
              color: "#3498db",
              height: "sm"
            }
          ]
        }
      }
    };

    // 執行推送
    const pushData = {
      to: pushTarget,
      messages: [simplifiedFlexMessage]
    };

    const response = UrlFetchApp.fetch('https://api.line.me/v2/bot/message/push', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${config.lineChannelAccessToken}`
      },
      payload: JSON.stringify(pushData),
      muteHttpExceptions: true  // 避免異常中斷
    });

    if (response.getResponseCode() === 200) {
      console.log(`✅ [v1.6.0] 簡化版 Push API 推送成功`);
      return { 
        success: true, 
        method: 'push_api_simplified',
        quotaUsed: true 
      };
    } else {
      console.error(`❌ [v1.6.0] 簡化版 Push API 推送失敗:`, response.getContentText());
      return { success: false, error: response.getContentText() };
    }

  } catch (error) {
    console.error('❌ [v1.6.0] pushSimplifiedImageResult 執行失敗:', error);
    return { success: false, error: error.message };
  }
}

// ===== 🚨 錯誤處理函數 =====

/**
 * 🚨 構建 Push API 失敗時的用戶通知訊息
 */
function buildPushFailureMessage(errorText, imageResult, userOriginalPrompt) {
  try {
    // 識別錯誤類型
    let errorType = '系統錯誤';
    let userMessage = '';

    if (errorText.includes('monthly limit') || errorText.includes('429')) {
      errorType = 'API 配額用完';
      userMessage = `🎨 圖片生成完成，但推送失敗

📝 您的描述：${userOriginalPrompt}
✅ 圖片已成功生成！

❌ 推送失敗原因：LINE Bot 月度配額已用完
📅 配額將在下月 1 號重置

🖼️ 圖片連結：
${imageResult.url || imageResult.imageUrl || '生成失敗'}

💡 您可以點擊上方連結查看圖片
⏰ 或等待下月配額重置後重新嘗試`;
    } else if (errorText.includes('400')) {
      errorType = '請求格式錯誤';
      userMessage = `🎨 圖片生成完成，但顯示失敗

📝 您的描述：${userOriginalPrompt}
✅ 圖片已成功生成！

❌ 顯示失敗原因：訊息格式問題
🖼️ 圖片連結：
${imageResult.url || imageResult.imageUrl || '生成失敗'}

💡 請點擊上方連結查看圖片`;
    } else {
      userMessage = `🎨 圖片生成完成，但推送失敗

📝 您的描述：${userOriginalPrompt}
✅ 圖片已成功生成！

❌ 推送失敗，但圖片正常
🖼️ 圖片連結：
${imageResult.url || imageResult.imageUrl || '生成失敗'}

💡 請點擊上方連結查看圖片
🔧 如持續發生請聯繫管理員`;
    }

    console.log(`🚨 構建錯誤通知訊息，類型: ${errorType}`);
    return userMessage;

  } catch (error) {
    console.error('構建錯誤訊息失敗:', error);
    return `🎨 圖片生成遇到問題

📝 您的描述：${userOriginalPrompt}
❌ 系統暫時無法正常推送結果

💡 請稍後重試或聯繫管理員
🙏 造成不便敬請見諒`;
  }
}

/**
 * 🚨 構建 Flex Message 推送失敗時的通知訊息
 */
function buildFlexFailureMessage(errorText) {
  try {
    if (errorText.includes('monthly limit') || errorText.includes('429')) {
      return `🚨 功能暫時無法使用

❌ 原因：LINE Bot 月度配額已用完
📅 配額將在下月 1 號重置

💡 您可以：
• 等待下月配額重置
• 使用其他功能（不受影響）
• 聯繫管理員了解詳情

🙏 造成不便敬請見諒`;
    } else if (errorText.includes('400')) {
      return `🚨 按鈕功能暫時故障

❌ 原因：訊息格式問題
🔧 系統正在自動修復

💡 您可以：
• 稍後重試
• 使用其他功能
• 聯繫管理員回報問題`;
    } else {
      return `🚨 按鈕功能暫時無法使用

❌ 系統遇到技術問題
🔧 正在處理中

💡 請稍後重試或聯繫管理員
🙏 造成不便敬請見諒`;
    }
  } catch (error) {
    console.error('構建 Flex 錯誤訊息失敗:', error);
    return `🚨 系統暫時無法正常運作

❌ 遇到未知問題
💡 請稍後重試或聯繫管理員`;
  }
}

/**
 * 🚨 構建文字推送失敗時的通知訊息
 */
function buildTextFailureMessage(errorText, originalText) {
  try {
    if (errorText.includes('monthly limit') || errorText.includes('429')) {
      return `🚨 訊息推送失敗

❌ 原因：LINE Bot 月度配額已用完
📅 配額將在下月 1 號重置

📋 原本要發送的內容：
${originalText}

💡 您可以手動複製上方內容`;
    } else if (errorText.includes('400')) {
      return `🚨 訊息推送失敗

❌ 原因：訊息格式問題
🔧 系統正在自動修復

📋 原本要發送的內容：
${originalText}

💡 請手動複製上方內容`;
    } else {
      return `🚨 訊息推送失敗

❌ 系統遇到技術問題
🔧 正在處理中

📋 原本要發送的內容：
${originalText}

💡 請手動複製上方內容`;
    }
  } catch (error) {
    console.error('構建文字錯誤訊息失敗:', error);
    return `🚨 系統暫時無法正常推送訊息

📋 原本要發送的內容：
${originalText}

💡 請手動複製上方內容`;
  }
}

// ===== 📋 模組說明 v1.6.0 =====
// 🎯 v1.6.0 重大修復：Reply API 優先策略
// 
// 📈 修復效果：
// - ✅ 所有推送函數都優先檢查 replyToken
// - ✅ 優先使用 Reply API（不占配額）
// - ✅ 預計節省 70% 以上的 Push API 配額
// - ✅ 避免 Invalid reply token 錯誤
// - ✅ 統一錯誤處理和回退機制
//
// 🔧 修復的函數：
// 1. pushImageResultV143 - 圖片結果推送（核心修復）
// 2. pushImageErrorV143 - 圖片錯誤訊息推送
// 3. pushImageSystemErrorV143 - 系統錯誤訊息推送
// 4. pushMessage- 文字訊息推送（根源修復）
// 5. pushFlexMessageV143 - Flex Message 推送
// 6. pushSimplifiedImageResult - 簡化版圖片推送
//
// 🎉 本次修復將大幅提升系統效率和用戶體驗！
