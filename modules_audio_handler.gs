// == AI 語音處理模組 ==
// 🔊 專門處理語音相關功能的 AI 處理函數
// 🔧 v1.4.3 - 語音處理專用版
// 📁 本檔案職責：TTS 處理、對話音頻處理、語音相關輔助函數

// ===== 🔊 語音處理函數 =====

/**
 * 🔊 AI 驅動的 TTS 處理
 * 返回特殊物件以觸發音頻回覆
 */
function handleAITTSRequest(intent, originalMessage, userId, sourceType) {
  try {
    console.log(`🔊 處理 TTS 請求: ${originalMessage}`);

    // 提取要轉換的文本
    const textToConvert = extractTextForTTS(originalMessage);

    if (!textToConvert || textToConvert.trim() === '') {
      return '🤔 我理解您想要語音轉換，但沒有找到要轉換的文字。請告訴我要轉換什麼內容？';
    }

    // 🔧 修復：調用 TTS 功能（使用正確的函數名稱）
    const startTime = Date.now();
    const ttsResult = textToSpeechWithGemini(textToConvert);
    const processingTime = Date.now() - startTime;

    if (ttsResult.success) {
      logActivity('Reply', 'TTS轉換', 'Success', 'audio', 'handleAITTSRequest', `用戶${userId}(${sourceType}): ${textToConvert.substring(0, 50)}...`, processingTime);

      // 🎯 返回特殊物件以觸發 AI-First 音頻回覆
      return {
        type: 'audio_response',
        audioResult: ttsResult,
        originalText: textToConvert
      };
    } else {
      return `❌ 語音轉換失敗：${ttsResult.error}

💡 請稍後再試，或者換個方式表達`;
    }

  } catch (error) {
    console.error('AI TTS 處理失敗:', error);
    return '我理解您想要語音轉換，但處理時遇到了問題。請稍後再試？';
  }
}

/**
 * 🎙️ AI 驅動的對話音頻處理
 * 使用 Native Audio Dialog 模型
 * 🔧 v1.3.2 修復：統一使用 Utils.gs 中的正確實現
 */
function handleAIConversationalAudio(intent, originalMessage, userId, sourceType) {
  try {
    console.log(`🎙️ 處理對話音頻請求: ${originalMessage}`);

    // 🚀 修復：直接調用 Utils.gs 中的 Native Audio Dialog 模型
    const conversationContext = {
      userId: userId,
      sourceType: sourceType,
      conversationHistory: [] // 可以添加對話歷史
    };
    
    const startTime = Date.now();
    const audioDialogResult = callGeminiAudioDialog(originalMessage, conversationContext);
    const processingTime = Date.now() - startTime;

    if (audioDialogResult.success) {
      logActivity('Reply', 'Native Audio Dialog', 'Success', 'audio', 'handleAIConversationalAudio', `用戶${userId}(${sourceType}): ${originalMessage.substring(0, 50)}...`, processingTime);

      // 🎯 返回特殊物件以觸發 AI-First 音頻回覆
      return {
        type: 'audio_response',
        audioResult: audioDialogResult,
        originalText: originalMessage,
        mode: 'native_audio_dialog'
      };
    } else {
      console.log(`🔄 Native Audio Dialog 失敗，使用備用方案: ${audioDialogResult.error}`);

      // 🔄 備用：使用傳統對話 + TTS 組合
      const fallbackStartTime = Date.now();
      const textResponse = callGemini(originalMessage, 'general');

      // 🔧 修復：使用正確的 TTS 函數名稱
      const ttsResult = textToSpeechWithGemini(textResponse);
      const fallbackProcessingTime = Date.now() - fallbackStartTime;

      if (ttsResult.success) {
        logActivity('Reply', '對話音頻備用', 'Success', 'audio', 'handleAIConversationalAudio', `用戶${userId}(${sourceType}): ${originalMessage.substring(0, 30)}... → ${textResponse.substring(0, 30)}...`, fallbackProcessingTime);

        return {
          type: 'audio_response',
          audioResult: ttsResult,
          originalText: textResponse,
          mode: 'conversational_fallback',
          userQuery: originalMessage
        };
      } else {
        return `🎙️ 我想用語音回應您，但語音功能暫時有問題。

📝 文字回應：${textResponse}`;
      }
    }

  } catch (error) {
    console.error('AI 對話音頻處理失敗:', error);
    return '我理解您想要語音對話，但處理時遇到了問題。請稍後再試？';
  }
}

/**
 * 🧪 測試語音功能完整性
 */
function testAudioHandlers_debug() {
  console.log('🧪 === 測試語音處理函數 ===');
  
  const testResults = {
    timestamp: new Date().toISOString(),
    tests: {},
    summary: {
      total: 0,
      passed: 0,
      failed: 0
    }
  };
  
  // 測試語音處理函數
  const audioHandlers = [
    'handleAITTSRequest',
    'handleAIConversationalAudio'
  ];
  
  audioHandlers.forEach(handlerName => {
    testResults.summary.total++;
    try {
      const func = eval(handlerName);
      if (typeof func === 'function') {
        testResults.tests[handlerName] = { status: 'PASS', error: null };
        testResults.summary.passed++;
        console.log(`✅ ${handlerName}: 函數存在且可調用`);
      } else {
        testResults.tests[handlerName] = { status: 'FAIL', error: '不是函數' };
        testResults.summary.failed++;
        console.log(`❌ ${handlerName}: 不是函數`);
      }
    } catch (error) {
      testResults.tests[handlerName] = { status: 'FAIL', error: error.message };
      testResults.summary.failed++;
      console.log(`❌ ${handlerName}: ${error.message}`);
    }
  });
  
  // 測試依賴函數
  const dependencyFunctions = [
    'extractTextForTTS',
    'textToSpeechWithGemini',
    'callGeminiAudioDialog',
    'logActivity'
  ];
  
  console.log('\n🔗 檢查依賴函數...');
  dependencyFunctions.forEach(funcName => {
    testResults.summary.total++;
    try {
      const func = eval(funcName);
      if (typeof func === 'function') {
        testResults.tests[funcName] = { status: 'PASS', error: null };
        testResults.summary.passed++;
        console.log(`✅ ${funcName}: 可訪問`);
      } else {
        testResults.tests[funcName] = { status: 'FAIL', error: '不是函數' };
        testResults.summary.failed++;
        console.log(`❌ ${funcName}: 不是函數`);
      }
    } catch (error) {
      testResults.tests[funcName] = { status: 'FAIL', error: error.message };
      testResults.summary.failed++;
      console.log(`❌ ${funcName}: ${error.message}`);
    }
  });
  
  // 生成測試報告
  const successRate = testResults.summary.total > 0 ? 
    ((testResults.summary.passed / testResults.summary.total) * 100).toFixed(1) : '0.0';
  
  console.log('\n📊 語音功能測試結果:');
  console.log(`   總測試數: ${testResults.summary.total}`);
  console.log(`   通過: ${testResults.summary.passed}`);
  console.log(`   失敗: ${testResults.summary.failed}`);
  console.log(`   成功率: ${successRate}%`);
  
  if (testResults.summary.failed > 0) {
    console.log('\n❌ 失敗的測試:');
    Object.entries(testResults.tests).forEach(([name, result]) => {
      if (result.status === 'FAIL') {
        console.log(`   • ${name}: ${result.error}`);
      }
    });
  }
  
  return testResults;
}

/**
 * 📋 語音處理模組說明
 * 
 * 🎯 本檔案職責：
 * - TTS 處理 (handleAITTSRequest)
 * - 對話音頻處理 (handleAIConversationalAudio)
 * - 語音功能測試 (testAudioHandlers_debug)
 * 
 * 🔗 依賴檔案：
 * - Utils.gs (textToSpeechWithGemini, callGeminiAudioDialog, logActivity)
 * - AIHandlers_Content.gs (extractTextForTTS)
 * - GeminiAdvanced.gs (語音模型實現)
 * 
 * 📏 檔案大小：約 8KB
 * 🎉 重構版本：從 AIHandlers_Specialized.gs 拆分而來
 * 
 * 🔊 支援的語音功能：
 * - 文字轉語音 (TTS)
 * - Native Audio Dialog 對話
 * - 備用方案處理
 * - 特殊回應物件生成
 */
