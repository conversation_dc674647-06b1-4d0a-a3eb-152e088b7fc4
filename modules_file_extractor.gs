// == 統一內容提取模組 ==
// 🎯 集中管理所有內容提取邏輯，消除重複代碼
// 🚀 v1.5.1 - AI-First 架構重構的一部分

/**
 * 🔊 統一的 TTS 文字提取
 * 支援多種自然語言格式，不依賴特定標點符號
 */
function extractTextForTTS(originalMessage) {
  try {
    console.log(`🔊 提取 TTS 文字: ${originalMessage}`);
    
    // 🎯 優先使用 AI 智能提取
    const aiExtracted = callAIWithPrompt('TTS_TEXT_EXTRACTION', {
      message: originalMessage
    });
    
    if (aiExtracted && aiExtracted.trim() && aiExtracted !== originalMessage) {
      console.log(`✅ AI 提取成功: "${aiExtracted}"`);
      return aiExtracted.trim();
    }
    
    // 🛡️ 備用：智能正則提取
    return extractTTSTextWithRegex(originalMessage);
    
  } catch (error) {
    console.error('TTS 文字提取失敗:', error);
    // 🔄 最終備用：正則提取
    return extractTTSTextWithRegex(originalMessage);
  }
}

/**
 * 🛡️ 備用的正則表達式提取邏輯
 * 支援多種格式：你說：XXX、你說"XXX"、你說 XXX
 */
function extractTTSTextWithRegex(originalMessage) {
  try {
    let extractedText = originalMessage;
    
    // 🎯 智能移除 TTS 指令詞（支援多種格式）
    const ttsPatterns = [
      /^(你說|你念|念出來|讀出來|語音|播放|說出來|tts)[：:：""\s]*(.+)$/i,
      /^(語音轉換|語音播放)[：:：""\s]*(.+)$/i,
      /^(幫我說|幫我念)[：:：""\s]*(.+)$/i
    ];
    
    for (const pattern of ttsPatterns) {
      const match = extractedText.match(pattern);
      if (match && match[2]) {
        extractedText = match[2].trim();
        break;
      }
    }
    
    // 🧹 清理多餘的標點符號
    extractedText = extractedText
      .replace(/^["'「『]/g, '')  // 移除開頭引號
      .replace(/["'」』]$/g, '')  // 移除結尾引號
      .replace(/^[：:]\s*/g, '')  // 移除開頭冒號
      .trim();
    
    // 🔍 如果提取後為空或太短，返回原始訊息
    if (!extractedText || extractedText.length < 1) {
      console.log(`⚠️ 提取結果為空，使用原始訊息`);
      return originalMessage;
    }
    
    console.log(`🛡️ 正則提取結果: "${extractedText}"`);
    return extractedText;
    
  } catch (error) {
    console.error('正則提取失敗:', error);
    return originalMessage;
  }
}

/**
 * 🎨 統一的圖片提示詞提取
 */
function extractImagePrompt(originalMessage) {
  try {
    console.log(`🎨 [提示詞處理] 開始提取圖片提示詞`);
    console.log(`📝 [輸入] 用戶原始訊息: "${originalMessage}"`);

    // 🎯 優先使用 AI 智能提取
    console.log(`🤖 [處理] 使用 IMAGE_PROMPT_EXTRACTION 提示詞進行AI處理...`);
    const aiExtracted = callAIWithPrompt('IMAGE_PROMPT_EXTRACTION', {
      message: originalMessage
    });

    console.log(`📤 [輸出] AI處理結果: "${aiExtracted}"`);

    if (aiExtracted && aiExtracted.trim() && aiExtracted !== originalMessage) {
      console.log(`✅ [成功] AI提取並翻譯成功`);
      console.log(`🔄 [對比] 原始: "${originalMessage}" → 處理後: "${aiExtracted.trim()}"`);
      return aiExtracted.trim();
    }

    // 🛡️ 備用：正則提取
    console.log(`⚠️ [備用] AI提取無效，使用正則提取`);
    const regexResult = extractImagePromptWithRegex(originalMessage);
    console.log(`📤 [輸出] 正則提取結果: "${regexResult}"`);
    return regexResult;

  } catch (error) {
    console.error('❌ [錯誤] 圖片提示詞提取失敗:', error);
    const regexResult = extractImagePromptWithRegex(originalMessage);
    console.log(`📤 [備用輸出] 正則提取結果: "${regexResult}"`);
    return regexResult;
  }
}

/**
 * 🛡️ 備用的圖片提示詞正則提取
 */
function extractImagePromptWithRegex(originalMessage) {
  try {
    let extractedText = originalMessage;
    
    // 🎯 移除圖片生成指令詞
    const imagePatterns = [
      /^[!！]?(畫圖|畫一|畫個|畫張|畫|生成圖|生成|創建|製作|給我)[圖片圖像]*[：:：\s]*(.+)$/i,
      /^[!！]?(幫我畫|幫我生成)[：:：\s]*(.+)$/i,
      /^[!！]?(圖片|圖像)[：:：\s]*(.+)$/i
    ];
    
    for (const pattern of imagePatterns) {
      const match = extractedText.match(pattern);
      if (match && match[2]) {
        extractedText = match[2].trim();
        break;
      }
    }
    
    // 🧹 清理標點符號
    extractedText = extractedText
      .replace(/^["'「『]/g, '')
      .replace(/["'」』]$/g, '')
      .replace(/^[：:]\s*/g, '')
      .trim();
    
    if (!extractedText || extractedText.length < 1) {
      return originalMessage;
    }
    
    console.log(`🛡️ 圖片正則提取結果: "${extractedText}"`);
    return extractedText;
    
  } catch (error) {
    console.error('圖片正則提取失敗:', error);
    return originalMessage;
  }
}

/**
 * 📝 統一的筆記內容提取
 */
function extractNoteContent(originalMessage) {
  try {
    console.log(`📝 提取筆記內容: ${originalMessage}`);
    
    let extractedText = originalMessage;
    
    // 🎯 移除筆記指令詞
    const notePatterns = [
      /^(記錄|記住|筆記|記下)[：:：\s]*(.+)$/i,
      /^(幫我記|幫我記錄)[：:：\s]*(.+)$/i,
      /^(note|記)[：:：\s]*(.+)$/i
    ];
    
    for (const pattern of notePatterns) {
      const match = extractedText.match(pattern);
      if (match && match[2]) {
        extractedText = match[2].trim();
        break;
      }
    }
    
    // 🧹 清理標點符號
    extractedText = extractedText
      .replace(/^["'「『]/g, '')
      .replace(/["'」』]$/g, '')
      .replace(/^[：:]\s*/g, '')
      .trim();
    
    if (!extractedText || extractedText.length < 1) {
      return originalMessage;
    }
    
    console.log(`📝 筆記提取結果: "${extractedText}"`);
    return extractedText;
    
  } catch (error) {
    console.error('筆記內容提取失敗:', error);
    return originalMessage;
  }
}

/**
 * 🔍 統一的搜索關鍵字提取
 */
function extractSearchKeywords(originalMessage) {
  try {
    console.log(`🔍 提取搜索關鍵字: ${originalMessage}`);
    
    let extractedText = originalMessage;
    
    // 🎯 移除搜索指令詞
    const searchPatterns = [
      /^(搜索|搜尋|查找|找|search)[：:：\s]*(.+)$/i,
      /^(幫我找|幫我搜)[：:：\s]*(.+)$/i,
      /^(查詢|query)[：:：\s]*(.+)$/i
    ];
    
    for (const pattern of searchPatterns) {
      const match = extractedText.match(pattern);
      if (match && match[2]) {
        extractedText = match[2].trim();
        break;
      }
    }
    
    // 🧹 清理標點符號
    extractedText = extractedText
      .replace(/^["'「『]/g, '')
      .replace(/["'」』]$/g, '')
      .replace(/^[：:]\s*/g, '')
      .trim();
    
    if (!extractedText || extractedText.length < 1) {
      return originalMessage;
    }
    
    console.log(`🔍 搜索關鍵字提取結果: "${extractedText}"`);
    return extractedText;
    
  } catch (error) {
    console.error('搜索關鍵字提取失敗:', error);
    return originalMessage;
  }
}

/**
 * 🧪 測試內容提取功能
 */
function testContentExtractor_debug() {
  console.log('🧪 === 測試統一內容提取模組 ===');
  
  const testCases = [
    // TTS 測試
    {
      category: 'TTS 文字提取',
      function: extractTextForTTS,
      tests: [
        { input: '你說：今天天氣很好', expected: '今天天氣很好' },
        { input: '你說"我想吃酸菜魚"', expected: '我想吃酸菜魚' },
        { input: '念出來 我愛台灣', expected: '我愛台灣' },
        { input: '語音播放：測試文字', expected: '測試文字' },
        { input: 'TTS: Hello World', expected: 'Hello World' },
        { input: '你說我想吃酸菜魚', expected: '我想吃酸菜魚' }
      ]
    },
    // 圖片測試
    {
      category: '圖片提示詞提取',
      function: extractImagePrompt,
      tests: [
        { input: '畫一隻可愛的小貓', expected: '一隻可愛的小貓' },
        { input: '生成圖片：夕陽下的海邊', expected: '夕陽下的海邊' },
        { input: '給我一張科幻風格的城市圖片', expected: '一張科幻風格的城市圖片' },
        { input: 'draw a beautiful sunset', expected: 'a beautiful sunset' }
      ]
    }
  ];
  
  let totalTests = 0;
  let passedTests = 0;
  
  testCases.forEach(category => {
    console.log(`\n📋 測試類別: ${category.category}`);
    
    category.tests.forEach(test => {
      totalTests++;
      try {
        const result = category.function(test.input);
        const passed = result.includes(test.expected) || test.expected.includes(result);
        
        if (passed) {
          passedTests++;
          console.log(`✅ "${test.input}" → "${result}"`);
        } else {
          console.log(`❌ "${test.input}" → "${result}" (期望包含: "${test.expected}")`);
        }
      } catch (error) {
        console.log(`🚨 "${test.input}" → 錯誤: ${error.message}`);
      }
    });
  });
  
  const successRate = ((passedTests / totalTests) * 100).toFixed(1);
  console.log(`\n🎯 測試結果: ${passedTests}/${totalTests} (${successRate}%)`);
  
  return {
    total: totalTests,
    passed: passedTests,
    failed: totalTests - passedTests,
    successRate: successRate
  };
}

/**
 * 📋 內容提取模組說明
 * 
 * 🎯 本檔案職責：
 * - 統一所有內容提取邏輯
 * - 消除重複代碼
 * - 支援 AI-First 和備用正則提取
 * 
 * 🔗 替代的重複函數：
 * - helper_functions.gs 中的 extractTextForTTS
 * - AIHandlers_Content.gs 中的 extractTextForTTS
 * - helper_functions_v13.gs 中的 extractTextForTTS
 * - AIHandlers_Specialized.gs 中的 extractTextForTTS
 * - GeminiAdvanced.gs 中的 extractTextForTTS
 * 
 * 📏 檔案大小：約 12KB
 * 🎉 重構版本：v1.5.1 統一內容提取
 */
