// == Gemini 進階功能模組 ==
// 🎯 實現 TTS（文本轉語音）和圖像生成功能
// 🤖 整合到智能處理中樞，根據用戶意圖自動選擇功能
// 🔧 v2.9 - 修復API安全性錯誤處理和提示詞日誌記錄

/**
 * 🌩️ GCP Access Token 產生函數（Imagen API 專用）
 * 建立並取得用於呼叫 GCP API 的 OAuth2 Access Token。
 * 這是透過手動加入的 OAuth2.gs 程式碼和指令碼屬性來運作的。
 * @returns {string} The access token.
 */
function getGcpAccessToken() {
  const scriptProperties = PropertiesService.getScriptProperties();
  const privateKeyRaw = scriptProperties.getProperty('GCP_PRIVATE_KEY');
  const clientEmail = scriptProperties.getProperty('GCP_CLIENT_EMAIL');

  if (!privateKeyRaw || !clientEmail) {
    throw new Error(`❌ GCP 認證設定不完整！

請在 Apps Script 的「專案設定」→「指令碼屬性」中添加：
• GCP_PRIVATE_KEY: 您的 GCP 服務帳號私鑰
• GCP_CLIENT_EMAIL: 您的 GCP 服務帳號電子郵件

參考手冊：Google Apps Script 中整合 Imagen API 施行手冊 (v2 - 手動認證版)`);
  }

  // ==================== 關鍵修改處 ====================
  // 💡 自動修復私鑰中的換行符，將 "\\n" 字串轉換為真正的換行符 "\n"
  const privateKey = privateKeyRaw.replace(/\\n/g, '\n');
  // ====================================================

  try {
    console.log('🔐 開始生成 GCP Access Token...');

    // OAuth2 物件來自我們手動加入的 OAuth2.gs 檔案
    const service = OAuth2.createService('GCP')
        // 設定認證伺服器
        .setTokenUrl('https://oauth2.googleapis.com/token')
        // 設定私鑰和頒發者
        .setPrivateKey(privateKey) // ⬅️ 使用修正格式後的 privateKey
        .setIssuer(clientEmail)
        // 設定要請求的權限範圍
        .setScope('https://www.googleapis.com/auth/cloud-platform')
        // 設定屬性儲存
        .setPropertyStore(PropertiesService.getScriptProperties());

    if (service.hasAccess()) {
      // 檢查 Token 是否過期，如果過期會自動刷新
      const accessToken = service.getAccessToken();
      console.log('✅ GCP Access Token 生成成功');
      return accessToken;
    } else {
      // 如果沒有 Token 或已過期，拋出錯誤
      const lastError = service.getLastError();
      throw new Error(`❌ GCP 認證失敗: ${lastError}

可能的原因：
1. 私鑰格式不正確（需包含完整的 -----BEGIN PRIVATE KEY----- 和 -----END PRIVATE KEY----- 標記）
2. 服務帳號電子郵件不正確
3. 服務帳號權限不足

請檢查您的 GCP 服務帳號設定和指令碼屬性`);
    }
  } catch (error) {
    console.error('❌ GCP Access Token 生成失敗:', error);
    throw new Error(`❌ GCP 認證過程發生錯誤: ${error.message}

請確認：
1. OAuth2.gs 檔案已正確加入專案
2. 指令碼屬性中的 GCP_PRIVATE_KEY 和 GCP_CLIENT_EMAIL 已正確設定
3. 網路連線正常`);
  }
}

/**
 * 🔊 Gemini TTS - 文本轉語音功能（修復版）
 * 使用 Gemini 2.5 TTS 模型將文本轉換為語音，正確處理 PCM 數據並上傳到 Cloudinary
 * 🔧 修復：統一使用 getConfig().ttsModel 從試算表讀取模型配置
 */
function textToSpeechWithGemini(text, voiceConfig = {}) {
  try {
    const config = getConfig();
    if (!config.geminiApiKey) {
      throw new Error('需要 Gemini API Key 才能使用 TTS 功能');
    }
    
    console.log(`🔊 開始 TTS 轉換: ${text.substring(0, 50)}...`);
    
    // 🎯 修復：直接從配置讀取 TTS 模型（試算表 B16）
    const modelName = config.ttsModel;
    console.log(`🤖 使用 TTS 模型: ${modelName}`);
    
    const apiVersion = getModelApiVersion(modelName);
    const url = `https://generativelanguage.googleapis.com/${apiVersion}/models/${modelName}:generateContent?key=${config.geminiApiKey}`;
    
    // 正確的 TTS 配置
    const payload = JSON.stringify({
      contents: [{
        parts: [{ text: text }]
      }],
      generationConfig: {
        responseModalities: ["AUDIO"],
        speechConfig: {
          voiceConfig: {
            prebuiltVoiceConfig: {
              voiceName: voiceConfig.voiceName || 'Kore'
            }
          }
        }
      }
    });
    
    const response = UrlFetchApp.fetch(url, {
      method: 'POST',
      contentType: 'application/json',
      payload: payload,
      muteHttpExceptions: true
    });
    
    if (response.getResponseCode() !== 200) {
      throw new Error(`TTS API 錯誤: ${response.getResponseCode()} - ${response.getContentText()}`);
    }
    
    const result = JSON.parse(response.getContentText());

    // 檢查回應格式
    if (!result.candidates || !result.candidates[0]) {
      throw new Error('TTS API 回應格式錯誤');
    }

    // 查找音頻數據
    const audioData = result.candidates[0].content.parts.find(part => part.inlineData);

    if (!audioData) {
      throw new Error('未生成音頻數據');
    }

    console.log(`📊 音頻 MIME 類型: ${audioData.inlineData.mimeType}`);

    // 🔧 解碼音頻數據並轉換為正確的 WAV 格式
    const rawAudioBytes = Utilities.base64Decode(audioData.inlineData.data);
    console.log(`📏 原始音頻大小: ${rawAudioBytes.length} bytes`);

    // 🎯 根據 MIME 類型解析音頻參數
    const audioParams = parseAudioMimeType(audioData.inlineData.mimeType);
    console.log(`🎵 音頻參數:`, audioParams);

    // 🔧 創建正確的 WAV 檔案
    const wavBlob = createWAVFromPCM(rawAudioBytes, audioParams);
    console.log(`✅ WAV 檔案已創建，大小: ${wavBlob.getBytes().length} bytes`);

    // 🔧 新增：上傳到 Cloudinary 並轉換為 M4A 格式
    let cloudinaryUrl = null;
    let cloudinaryError = null;
    
    try {
      cloudinaryUrl = uploadAudioToCloudinary(wavBlob, text);
      console.log(`✅ Cloudinary 上傳成功: ${cloudinaryUrl}`);
    } catch (cloudError) {
      cloudinaryError = cloudError.message;
      console.log(`⚠️ Cloudinary 上傳失敗: ${cloudinaryError}`);
    }

    // 備用：保存到 Google Drive
    const driveFolder = DriveApp.getFolderById(config.folderId);
    const file = driveFolder.createFile(wavBlob);
    file.setName(`Gemini_TTS_${new Date().getTime()}.wav`);

    console.log(`✅ TTS 轉換成功，檔案 ID: ${file.getId()}`);

    return {
      success: true,
      // 🎯 優先使用 Cloudinary URL（LINE Bot 可播放）
      audioUrl: cloudinaryUrl || file.getUrl(),
      cloudinaryUrl: cloudinaryUrl,
      driveUrl: file.getUrl(),
      fileId: file.getId(),
      fileName: file.getName(),
      mimeType: cloudinaryUrl ? 'audio/mp4' : 'audio/wav', // M4A 格式
      text: text,
      isPlayable: !!cloudinaryUrl, // 是否可在 LINE 中播放
      cloudinaryError: cloudinaryError,
      audioParams: audioParams
    };
    
  } catch (error) {
    console.error('TTS 生成錯誤:', error);
    return {
      success: false,
      error: error.message,
      text: text,
      isPlayable: false
    };
  }
}

/**
 * 🎵 解析音頻 MIME 類型參數
 * 例如：audio/L16;codec=pcm;rate=24000
 */
function parseAudioMimeType(mimeType) {
  const params = {
    sampleRate: 24000,  // 預設 24kHz（Gemini 常用）
    bitsPerSample: 16,  // 預設 16 位
    channels: 1,        // 預設單聲道
    format: 'pcm'       // 預設 PCM
  };

  try {
    // 解析 MIME 類型字符串
    if (mimeType.includes('rate=')) {
      const rateMatch = mimeType.match(/rate=(\d+)/);
      if (rateMatch) {
        params.sampleRate = parseInt(rateMatch[1]);
      }
    }

    // L16 表示 16 位線性 PCM
    if (mimeType.includes('L16')) {
      params.bitsPerSample = 16;
      params.format = 'pcm';
    }

    console.log(`🎵 解析的音頻參數: ${params.sampleRate}Hz, ${params.bitsPerSample}位, ${params.channels}聲道`);

  } catch (error) {
    console.log(`⚠️ MIME 類型解析失敗，使用預設參數: ${error.message}`);
  }

  return params;
}

/**
 * 🔧 從 PCM 數據創建 WAV 檔案
 */
function createWAVFromPCM(pcmBytes, audioParams) {
  try {
    const sampleRate = audioParams.sampleRate;
    const bitsPerSample = audioParams.bitsPerSample;
    const numChannels = audioParams.channels;
    const byteRate = sampleRate * numChannels * bitsPerSample / 8;
    const blockAlign = numChannels * bitsPerSample / 8;
    const dataSize = pcmBytes.length;
    const fileSize = 36 + dataSize;

    console.log(`🔧 創建 WAV 標頭: ${sampleRate}Hz, ${bitsPerSample}位, ${numChannels}聲道, 數據大小: ${dataSize} bytes`);

    // 創建 WAV 標頭（44 字節）
    const wavHeader = new ArrayBuffer(44);
    const view = new DataView(wavHeader);

    // RIFF 標頭
    view.setUint8(0, 0x52);  // 'R'
    view.setUint8(1, 0x49);  // 'I'
    view.setUint8(2, 0x46);  // 'F'
    view.setUint8(3, 0x46);  // 'F'
    view.setUint32(4, fileSize, true);  // 檔案大小（小端序）

    // WAVE 格式
    view.setUint8(8, 0x57);   // 'W'
    view.setUint8(9, 0x41);   // 'A'
    view.setUint8(10, 0x56);  // 'V'
    view.setUint8(11, 0x45);  // 'E'

    // fmt 子區塊
    view.setUint8(12, 0x66);  // 'f'
    view.setUint8(13, 0x6D);  // 'm'
    view.setUint8(14, 0x74);  // 't'
    view.setUint8(15, 0x20);  // ' '
    view.setUint32(16, 16, true);  // fmt 區塊大小
    view.setUint16(20, 1, true);   // 音頻格式 (1 = PCM)
    view.setUint16(22, numChannels, true);  // 聲道數
    view.setUint32(24, sampleRate, true);   // 採樣率
    view.setUint32(28, byteRate, true);     // 位元組率
    view.setUint16(32, blockAlign, true);   // 區塊對齊
    view.setUint16(34, bitsPerSample, true); // 每樣本位數

    // data 子區塊
    view.setUint8(36, 0x64);  // 'd'
    view.setUint8(37, 0x61);  // 'a'
    view.setUint8(38, 0x74);  // 't'
    view.setUint8(39, 0x61);  // 'a'
    view.setUint32(40, dataSize, true);  // 數據大小

    // 合併標頭和音頻數據
    const headerBytes = new Uint8Array(wavHeader);
    const combinedBytes = new Uint8Array(headerBytes.length + pcmBytes.length);
    combinedBytes.set(headerBytes, 0);
    combinedBytes.set(pcmBytes, headerBytes.length);

    console.log(`✅ WAV 檔案創建完成，總大小: ${combinedBytes.length} bytes`);

    return Utilities.newBlob(combinedBytes, 'audio/wav', `TTS_${new Date().getTime()}.wav`);

  } catch (error) {
    console.error('WAV 創建失敗:', error);
    // 返回原始數據作為備用
    return Utilities.newBlob(pcmBytes, 'audio/wav', `Raw_TTS_${new Date().getTime()}.wav`);
  }
}

/**
 * 🔧 上傳音頻到 Cloudinary 並轉換格式（最終修復版）
 * 將 WAV 格式轉換為 M4A 格式以符合 LINE Bot 要求
 */
function uploadAudioToCloudinary(audioBlob, originalText) {
  const config = getConfig();
  
  // 檢查 Cloudinary 配置
  if (!config.cloudinaryCloudName || !config.cloudinaryApiKey || !config.cloudinaryApiSecret) {
    throw new Error('請在 APIKEY 工作表中設定 Cloudinary 配置（Cloud Name, API Key, API Secret）');
  }
  
  try {
    console.log(`🌩️ 上傳音頻到 Cloudinary...`);
    
    // 生成唯一的檔案名稱
    const timestamp = new Date().getTime();
    const publicId = `linebot/tts/audio_${timestamp}`;
    
    // 準備上傳參數
    const uploadParams = {
      public_id: publicId,
      resource_type: 'video', // Cloudinary 將音頻視為視頻資源
      format: 'm4a', // 🎯 轉換為 M4A 格式
      timestamp: Math.floor(Date.now() / 1000)
    };
    
    // 生成簽名（使用修復的簽名函數）
    const signature = generateCloudinarySignatureFixed(uploadParams, config.cloudinaryApiSecret);
    
    // 準備表單數據（最終修復：確保所有數字參數都是字符串）
    const formData = {
      file: audioBlob,
      public_id: publicId,
      resource_type: 'video',
      format: 'm4a',
      api_key: config.cloudinaryApiKey,
      timestamp: String(uploadParams.timestamp),  // 修復：確保 timestamp 為字符串格式
      signature: signature
    };
    
    console.log(`📤 上傳參數: Public ID = ${publicId}, Format = m4a, Timestamp = ${formData.timestamp}`);
    
    // 上傳到 Cloudinary
    const uploadUrl = `https://api.cloudinary.com/v1_1/${config.cloudinaryCloudName}/video/upload`;
    
    const response = UrlFetchApp.fetch(uploadUrl, {
      method: 'POST',
      payload: formData,
      muteHttpExceptions: true
    });
    
    const responseCode = response.getResponseCode();
    const responseText = response.getContentText();
    
    console.log(`📊 Cloudinary 回應代碼: ${responseCode}`);
    
    if (responseCode !== 200) {
      console.log(`❌ Cloudinary 錯誤詳情: ${responseText}`);
      throw new Error(`Cloudinary 上傳失敗: ${responseCode} - ${responseText}`);
    }
    
    const result = JSON.parse(responseText);
    
    if (!result.secure_url) {
      throw new Error('Cloudinary 未返回有效的 URL');
    }
    
    // 🎯 確保返回 M4A 格式的 URL
    let m4aUrl = result.secure_url;
    if (!m4aUrl.endsWith('.m4a')) {
      m4aUrl = m4aUrl.replace(/\.[^.]+$/, '.m4a');
    }
    
    console.log(`✅ Cloudinary 上傳成功: ${m4aUrl}`);
    logActivity('Cloudinary音頻上傳', `公開ID: ${publicId}, URL: ${m4aUrl}`);
    
    return m4aUrl;
    
  } catch (error) {
    console.error('Cloudinary 上傳錯誤:', error);
    throw error;
  }
}

/**
 * 🔐 生成 Cloudinary 簽名（修復版）
 * 修復：確保所有參數值都以字符串格式參與簽名計算，避免科學記數法問題
 */
function generateCloudinarySignature(params, apiSecret) {
  // 🔧 圖片上傳簽名：只排除 file 和 api_key（與音頻上傳不同）
  const excludeParams = ['file', 'api_key'];

  // 只包含應該參與簽名的參數
  const sortedParams = Object.keys(params)
    .filter(key => !excludeParams.includes(key))
    .sort()
    .map(key => `${key}=${String(params[key])}`)
    .join('&');

  const stringToSign = sortedParams + apiSecret;

  console.log(`🔐 圖片簽名字符串: ${stringToSign}`);

  // 使用 SHA-1 生成簽名
  return Utilities.computeDigest(Utilities.DigestAlgorithm.SHA_1, stringToSign)
    .map(byte => ('0' + (byte & 0xFF).toString(16)).slice(-2))
    .join('');
}

/**
 * 🚀 智能端點路由：根據模型類型選擇正確的 API 端點
 * @param {string} modelName 模型名稱
 * @returns {object} 包含端點配置的物件
 */
function getApiEndpointForModel(modelName) {
  const config = getConfig();
  
  if (modelName.startsWith('imagen-')) {
    // 🎨 Imagen 模型使用 Vertex AI 端點
    // 檢查必要的 Vertex AI 配置
    if (!config.gcpProjectId || !config.gcpRegion) {
      throw new Error(`❌ Imagen 模型需要 GCP 配置：
      
請在 APIKEY 工作表中添加：
• B21: GCP Project ID
• B22: GCP Region (如 us-central1)

或改用 Gemini 圖像生成模型：gemini-2.0-flash-preview-image-generation`);
    }
    
    return {
      platform: 'vertex_ai',
      baseUrl: `https://${config.gcpRegion}-aiplatform.googleapis.com`,
      method: 'predict',
      path: `/v1/projects/${config.gcpProjectId}/locations/${config.gcpRegion}/publishers/google/models/imagegeneration`,
      authType: 'gcp_token', // 需要 GCP Access Token
      description: 'Vertex AI - Imagen 模型'
    };
  } else {
    // 🤖 Gemini 模型使用 Google AI Studio 端點
    const apiVersion = getModelApiVersion(modelName);
    
    return {
      platform: 'google_ai_studio',
      baseUrl: 'https://generativelanguage.googleapis.com',
      method: 'generateContent',
      path: `/${apiVersion}/models/${modelName}`,
      authType: 'api_key', // 使用 API Key
      description: 'Google AI Studio - Gemini 模型'
    };
  }
}
