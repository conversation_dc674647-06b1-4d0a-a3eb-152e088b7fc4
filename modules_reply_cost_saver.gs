/*
 * 檔案: modules_reply_cost_saver.gs
 * 分類: reply_cost_saver
 * 功能開關: 功能開關_REPLY_COST_SAVER (保持原名稱避免中斷)
 * 描述: Reply成本節省系統 + 角色設定管理 - 當有人發言時搭便車用Reply API節省PUSH費用
 * 依賴: [modules_smart_responder.gs, core_utils.gs, modules_ai_prompts.gs, modules_image_generation.gs]
 * 最後更新: 2025-07-08 (v3.1 - 修復功能開關名稱，確保系統正常運作)
 */

// ===== 📋 配置常數 =====

/**
 * 🎭 角色設定工作表配置
 */
const CHARACTER_PROFILE_SHEET_CONFIG = {
  sheetName: '角色設定',
  headers: [
    '角色名稱', '性別', '年齡', '職業', '角色描述', '基礎圖像提示詞', '固定SEED', '優先級', '是否啟用',
    '聊天風格', '回應長度', '正式程度', '表情符號使用', '群組行為', '私聊行為', '禁用詞彙', '偏好話題', '避免話題'
  ]
};

/**
 * 🕐 活躍時段配置
 */
const ACTIVE_TIME_RANGES = {
  lunch: { start: 11, end: 14 },
  dinner: { start: 17, end: 20 },
  midnight: { start: 20, end: 11 } // 跨日
};

/**
 * 📊 頻率限制配置
 */
const FREQUENCY_LIMITS = {
  IMAGE: {
    lunch: 1,
    dinner: 1,
    midnight: 1
  }
};

// ===== 🔧 核心功能函數 =====

/**
 * 🚀 Reply成本節省群組對話分析 - 主入口
 * 🎯 當有人發言時，檢查是否可以搭便車用Reply API（節省PUSH費用）
 */
function analyze_group_conversation_with_reply_cost_saver(event, userId, sourceType) {
  try {
    console.log(`🚀 開始Reply成本節省分析: 用戶=${userId}, 來源=${sourceType}`);
    
    // 1. 檢查功能開關（保持原名稱避免中斷）
    const rawValue = getConfigValue('功能開關_REPLY_COST_SAVER', 'APIKEY');
    const isEnabled = rawValue && (
      rawValue === 'TRUE' ||
      rawValue === true ||
      rawValue.toString().trim().toUpperCase() === 'TRUE'
    );

    console.log(`🎛️ Reply成本節省系統功能開關: "${rawValue}" → ${isEnabled ? '✅ 啟用' : '❌ 關閉'}`);

    if (!isEnabled) {
      console.log('❌ Reply成本節省系統功能已關閉，使用原有智能回應');
      return analyze_group_conversation(event, userId, sourceType);
    }
    
    // 🎯 步驟1：優先檢查Saver邏輯（能搭便車嗎？）
    console.log('🎯 步驟1：優先檢查Reply成本節省機會（與話題類型無關）...');
    const replyCostSaverCheck = check_reply_cost_saver_opportunity(event);

    // 記錄Saver決策到活動日誌
    logActivity('System', 'Reply成本節省檢查', 'Success', 'saver', 'analyze_group_conversation_with_reply_cost_saver',
                `Saver決策: ${replyCostSaverCheck.canSaveReplyFee ? '✅可節省' : '❌不可節省'}, 原因: ${replyCostSaverCheck.reason}`);

    if (replyCostSaverCheck.canSaveReplyFee) {
      console.log('✅ 能搭便車！啟動Saver邏輯，用Reply API發送圖片（節省PUSH費用）');

      // 記錄Saver啟動日誌
      logActivity('System', 'Reply成本節省啟動', 'Success', 'saver', 'analyze_group_conversation_with_reply_cost_saver',
                  `啟動Saver邏輯，準備發送圖片，訊息: ${event.message?.text || '無'}`);

      // 保留所有判斷邏輯，發圖片（不管話題）
      const saverResponse = generate_reply_cost_saver_response('IMAGE', replyCostSaverCheck, event);

      // 🎯 關鍵：檢查Saver回應結果
      if (saverResponse.should_respond && saverResponse.reply_cost_saver_type) {
        // Saver成功，記錄成功日誌
        logActivity('System', 'Reply成本節省成功', 'Success', 'saver', 'analyze_group_conversation_with_reply_cost_saver',
                    `Saver成功發送圖片，類型: ${saverResponse.reply_cost_saver_type}, SEED: ${saverResponse.character_seed}`);
        return saverResponse;
      } else {
        // Saver失敗，記錄失敗原因（這是Saver的過濾邏輯）
        logActivity('System', 'Reply成本節省失敗', 'Failure', 'saver', 'analyze_group_conversation_with_reply_cost_saver',
                    `Saver過濾邏輯拒絕: ${saverResponse.reason}，將繼續智能回應邏輯`);
        console.log(`❌ Saver過濾邏輯拒絕: ${saverResponse.reason}，繼續智能邏輯`);
        // 不要return，讓它繼續執行智能邏輯
      }
    }

    console.log(`❌ 不能搭便車，原因: ${replyCostSaverCheck.reason}，繼續智能邏輯`);

    // 🎯 步驟2：智能回應邏輯（判斷話題是否應該回應）
    console.log('🎯 步驟2：執行智能回應邏輯（判斷話題是否應該回應）...');
    const smartResponse = analyze_group_conversation(event, userId, sourceType);

    if (smartResponse.should_respond) {
      console.log('🎯 智能回應需要回應，執行原本的智能回應');
    } else {
      console.log(`📝 群組此發言無須回復：${event.message?.text || '無訊息內容'}`);
    }

    return smartResponse;
    
  } catch (error) {
    console.error('❌ Reply成本節省分析失敗:', error);
    // 發生錯誤時回退到原有系統
    return analyze_group_conversation(event, userId, sourceType);
  }
}

/**
 * 🕐 獲取當前時間範圍
 */
function getCurrentTimeRange(currentTime = new Date()) {
  const hour = currentTime.getHours();
  
  // 檢查各個時段
  if (hour >= ACTIVE_TIME_RANGES.lunch.start && hour < ACTIVE_TIME_RANGES.lunch.end) {
    return 'lunch';
  }
  
  if (hour >= ACTIVE_TIME_RANGES.dinner.start && hour < ACTIVE_TIME_RANGES.dinner.end) {
    return 'dinner';
  }
  
  // 午夜時段（跨日）
  if (hour >= ACTIVE_TIME_RANGES.midnight.start || hour < ACTIVE_TIME_RANGES.midnight.end) {
    return 'midnight';
  }
  
  return null; // 非活躍時段
}

/**
 * 📊 檢查頻率限制
 */
function checkFrequencyLimit(contentType) {
  try {
    const timeRange = getCurrentTimeRange();
    
    if (!timeRange) {
      return {
        canSend: false,
        reason: '非活躍時段',
        timeRange: null,
        remaining: 0
      };
    }

    const limit = FREQUENCY_LIMITS[contentType]?.[timeRange] || 0;
    const keyPrefix = 'reply_cost_saver_image_sent'; // 統一使用這個key
    const sentCount = getSentCount(keyPrefix, timeRange);
    const remaining = Math.max(0, limit - sentCount);

    console.log(`📊 頻率限制檢查: 時段=${timeRange}, 限制=${limit}, 已發送=${sentCount}, 剩餘=${remaining}`);

    return {
      canSend: remaining > 0,
      reason: remaining > 0 ? null : `${timeRange}時段${contentType}已達上限(${limit}次)，已發送${sentCount}次`,
      timeRange: timeRange,
      remaining: remaining
    };

  } catch (error) {
    console.error('檢查頻率限制失敗:', error);
    return {
      canSend: false,
      reason: '頻率檢查系統錯誤',
      timeRange: null,
      remaining: 0
    };
  }
}

/**
 * 🎨 檢查Reply成本節省機會
 * 🔧 v3.0 - 移除話題判斷，專注於成本節省條件檢查
 */
function check_reply_cost_saver_opportunity(event) {
  try {
    console.log('🎨 檢查Reply成本節省機會...');

    const messageText = event.message?.text || '';

    // 1. 檢查頻率限制
    console.log('🔍 步驟1：檢查頻率限制...');
    const frequencyCheck = checkFrequencyLimit('IMAGE');

    // 記錄頻率檢查詳細結果到LOG
    logActivity('System', 'Reply成本節省頻率檢查', frequencyCheck.canSend ? 'Success' : 'Failure', 'saver',
                'check_reply_cost_saver_opportunity',
                `時段: ${frequencyCheck.timeRange}, 限制: ${FREQUENCY_LIMITS.IMAGE[frequencyCheck.timeRange] || 0}, 剩餘: ${frequencyCheck.remaining}, 結果: ${frequencyCheck.canSend ? '通過' : '拒絕'}`);

    if (!frequencyCheck.canSend) {
      const reason = `頻率限制: ${frequencyCheck.reason}`;
      console.log(`❌ ${reason}`);
      return { canSaveReplyFee: false, reason: reason };
    }
    console.log(`✅ 頻率檢查通過，剩餘次數: ${frequencyCheck.remaining}`);

    // 2. 檢查角色設定
    console.log('🔍 步驟2：檢查角色設定...');
    const activeProfile = getActiveCharacterProfile();
    if (!activeProfile) {
      const reason = '沒有啟用的角色設定';
      console.log(`❌ ${reason}`);
      return { canSaveReplyFee: false, reason: reason };
    }
    console.log(`✅ 角色設定檢查通過: ${activeProfile.characterName}`);

    // 3. 檢查基本條件（移除話題判斷，只檢查技術條件）
    console.log('🔍 步驟3：檢查基本技術條件（與話題無關）...');
    const basicConditionsCheck = check_basic_reply_conditions(messageText, activeProfile);

    if (!basicConditionsCheck.shouldTrigger) {
      const reason = `基本條件不符: ${basicConditionsCheck.reason}`;
      console.log(`❌ ${reason}`);
      return { canSaveReplyFee: false, reason: reason };
    }
    console.log(`✅ 基本條件檢查通過: ${basicConditionsCheck.reason}`);

    console.log('✅ Reply成本節省機會確認');
    return {
      canSaveReplyFee: true,
      reason: '符合Reply成本節省條件',
      timeRange: frequencyCheck.timeRange,
      remaining: frequencyCheck.remaining,
      activeProfile: activeProfile,
      forceTriggered: false
    };

  } catch (error) {
    console.error('檢查Reply成本節省機會失敗:', error);
    return { canSaveReplyFee: false, reason: `系統錯誤: ${error.message}` };
  }
}

/**
 * 🔍 檢查基本Reply條件（純粹為了節省費用）
 * 🎯 v3.2 - 移除所有排除邏輯，搭便車就是為了省錢，不應該有任何過濾
 */
function check_basic_reply_conditions(messageText, activeProfile) {
  try {
    // 🎯 搭便車的唯一目的：節省PUSH費用
    // 不應該有任何內容過濾邏輯！

    console.log('✅ 基本Reply條件檢查通過（無過濾邏輯，純粹節省費用）');
    return {
      shouldTrigger: true,
      reason: '搭便車節省費用（無內容限制）',
      confidence: 100
    };

  } catch (error) {
    console.error('基本Reply條件檢查失敗:', error);
    return { shouldTrigger: false, reason: `條件檢查錯誤: ${error.message}` };
  }
}

/**
 * 🎨 生成Reply成本節省回應
 */
function generate_reply_cost_saver_response(responseType, costSaverData, event) {
  try {
    console.log(`🎨 生成 ${responseType} Reply成本節省回應...`);

    if (responseType === 'IMAGE') {
      return generate_image_reply_cost_saver_response(costSaverData, event);
    }

    // 其他類型的回應可以在這裡擴展
    console.log(`❌ 不支援的回應類型: ${responseType}`);
    return { should_respond: false, reason: '不支援的回應類型' };

  } catch (error) {
    console.error('生成Reply成本節省回應失敗:', error);
    return { should_respond: false, reason: '回應生成錯誤' };
  }
}

/**
 * 🖼️ 生成圖片Reply成本節省回應
 */
function generate_image_reply_cost_saver_response(costSaverData, event) {
  try {
    console.log('🖼️ 生成圖片Reply成本節省回應...');

    // 記錄開始生成
    logActivity('System', 'Reply成本節省圖片生成開始', 'Success', 'image', 'generate_image_reply_cost_saver_response',
                `角色: ${costSaverData.activeProfile.characterName}, 訊息: ${event.message?.text || '無'}`);

    // 生成圖片提示詞
    console.log('🔍 步驟1：生成圖片提示詞...');
    const imagePrompt = generateContextualImagePrompt(costSaverData.activeProfile, event);
    if (!imagePrompt) {
      const reason = '圖片提示詞生成失敗';
      console.log(`❌ ${reason}`);
      logActivity('System', 'Reply成本節省圖片生成失敗', 'Failure', 'image', 'generate_image_reply_cost_saver_response', reason);
      return { should_respond: false, reason: reason };
    }

    console.log(`🎨 步驟2：使用提示詞生成圖片: ${imagePrompt.substring(0, 50)}...`);

    // 🎭 使用角色一致性系統獲取 SEED（統一邏輯）
    const finalSeed = getSeedValue(null, costSaverData.activeProfile);
    console.log(`🎲 Reply Cost Saver 使用角色一致性 SEED: ${finalSeed}`);

    // 生成圖片
    const imageResult = generateImageWithGemini(imagePrompt, {
      seed: finalSeed,
      aspectRatio: '1:1'
    });

    if (!imageResult || !imageResult.success) {
      const reason = `圖片生成失敗: ${imageResult?.error || '未知錯誤'}`;
      console.log(`❌ ${reason}`);
      logActivity('System', 'Reply成本節省圖片生成失敗', 'Failure', 'image', 'generate_image_reply_cost_saver_response', reason);
      return { should_respond: false, reason: reason };
    }

    // 🆕 步驟3：生成配圖故事（偽主動發圖鏈專用）
    console.log('📖 步驟3：生成配圖故事（偽主動發圖鏈）...');
    let imageStory_costSaver = null;
    try {
      imageStory_costSaver = generateImageStory(imagePrompt);
      console.log(`✅ 偽主動發圖鏈故事生成: ${imageStory_costSaver ? '成功' : '失敗'}`);
      if (imageStory_costSaver) {
        console.log(`📖 故事長度: ${imageStory_costSaver.length}`);
      }
    } catch (storyError) {
      console.warn('偽主動發圖鏈故事生成失敗:', storyError);
      imageStory_costSaver = null;
    }

    // 記錄發送次數
    recordSentCount('reply_cost_saver_image_sent', costSaverData.timeRange);
    console.log('📊 已記錄觸發次數');

    // 記錄活動日誌
    logActivity('System', 'Reply成本節省圖片生成', 'Success', 'image', 'generate_image_reply_cost_saver_response',
                `角色: ${costSaverData.activeProfile.characterName}, 時段: ${costSaverData.timeRange}, 故事: ${imageStory_costSaver ? '有' : '無'}`);

    console.log('✅ 圖片Reply成本節省回應生成成功');

    return {
      should_respond: true,
      reason: '圖片Reply成本節省觸發',
      response_content: imageResult.cloudinaryUrl || imageResult.driveUrl,
      response_type: 'image',
      reply_cost_saver_type: 'IMAGE',
      character_profile: costSaverData.activeProfile.characterName,
      character_seed: costSaverData.activeProfile.fixed_seed,
      time_range: costSaverData.timeRange,
      image_prompt: imagePrompt,
      image_story_costSaver: imageStory_costSaver  // 🆕 偽主動發圖鏈故事
    };

  } catch (error) {
    console.error('生成圖片Reply成本節省回應失敗:', error);
    return { should_respond: false, reason: '圖片回應生成錯誤' };
  }
}

/**
 * 📤 執行Reply成本節省回應
 */
function execute_reply_cost_saver_response(responseData, replyToken) {
  try {
    console.log('📤 執行Reply成本節省回應...');

    if (responseData.response_type === 'image') {
      // 🖼️ 偽主動發圖鏈專用處理
      console.log('🖼️ 處理偽主動發圖鏈回應...');

      const imageResult_costSaver = {
        cloudinaryUrl: responseData.response_content,
        driveUrl: responseData.response_content, // 備用
        imageStory: responseData.image_story_costSaver,  // 🆕 偽主動發圖鏈故事
        seed: responseData.character_seed,
        modelUsed: 'cost-saver-chain'  // 🆕 標識調用鏈
      };

      // 🆕 使用原始圖片提示詞作為顯示文字，而不是SEED
      const displayText_costSaver = responseData.image_prompt || `✨ SEED: ${responseData.character_seed || 'N/A'}`;

      console.log(`🔍 偽主動發圖鏈數據檢查:`);
      console.log(`  - imageStory: ${imageResult_costSaver.imageStory ? '有' : '無'}`);
      console.log(`  - displayText: ${displayText_costSaver.substring(0, 50)}...`);

      // 🆕 調用replyWithImage，正確傳遞故事參數
      replyWithImage(replyToken, imageResult_costSaver, displayText_costSaver, null, responseData.image_story_costSaver);

      // 記錄活動日誌
      logActivity('Reply', 'Reply成本節省圖片發送', 'Success', 'image', 'execute_reply_cost_saver_response',
                  `角色: ${responseData.character_profile}, SEED: ${responseData.character_seed}`);

      console.log('✅ Reply成本節省圖片回應執行完成');
      return true;
    }

    console.log(`❌ 不支援的回應類型: ${responseData.response_type}`);
    return false;

  } catch (error) {
    console.error('執行Reply成本節省回應失敗:', error);
    return false;
  }
}

// ===== 🎭 角色設定管理 =====

/**
 * 🎭 獲取啟用的角色設定
 */
function getActiveCharacterProfile() {
  try {
    console.log('🎭 獲取啟用角色...');

    const spreadsheet = SpreadsheetApp.getActiveSpreadsheet();
    const sheet = spreadsheet.getSheetByName(CHARACTER_PROFILE_SHEET_CONFIG.sheetName);

    if (!sheet) {
      console.log('❌ 找不到角色設定工作表');
      return null;
    }

    const dataRange = sheet.getDataRange();
    const values = dataRange.getValues();

    if (values.length <= 1) {
      console.log('❌ 角色設定工作表無資料');
      return null;
    }

    // 尋找啟用的角色（第9欄：是否啟用）
    for (let i = 1; i < values.length; i++) {
      const row = values[i];
      const isActive = row[8]; // I欄：是否啟用

      if (isActive && (isActive === 'TRUE' || isActive === true || isActive.toString().trim().toUpperCase() === 'TRUE')) {
        const profile = {
          characterName: row[0] || '',      // A欄：角色名稱
          gender: row[1] || '',             // B欄：性別
          age: row[2] || '',                // C欄：年齡
          occupation: row[3] || '',         // D欄：職業
          personality: row[4] || '',        // E欄：角色描述
          base_prompt: row[5] || '',        // F欄：基礎圖像提示詞
          fixed_seed: row[6] || '',         // G欄：固定SEED
          priority: row[7] || 1,            // H欄：優先級
          active: row[8] || '',             // I欄：是否啟用

          // 擴展欄位 (J-R)
          chatStyle: row[9] || '',          // J欄：聊天風格
          responseLength: row[10] || '',    // K欄：回應長度
          formalityLevel: row[11] || '',    // L欄：正式程度
          emojiUsage: row[12] || '',        // M欄：表情符號使用
          groupBehavior: row[13] || '',     // N欄：群組行為
          privateBehavior: row[14] || '',   // O欄：私聊行為
          forbiddenPhrases: row[15] || '',  // P欄：禁用詞彙
          preferredTopics: row[16] || '',   // Q欄：偏好話題
          avoidTopics: row[17] || ''        // R欄：避免話題
        };

        console.log(`🎭 獲取啟用角色: ${profile.characterName} (${profile.age}歲${profile.occupation})`);
        return profile;
      }
    }

    console.log('❌ 沒有找到啟用的角色');
    return null;

  } catch (error) {
    console.error('獲取啟用角色失敗:', error);
    return null;
  }
}

/**
 * 🎯 生成情境化的圖片提示詞
 * 🔧 v3.0 - 智能版：直接組合中文資料，讓AI處理翻譯和優化
 */
function generateContextualImagePrompt(activeProfile, event) {
  try {
    console.log('🎯 生成情境化圖片提示詞（智能版）...');

    const currentTime = new Date();
    const timeRange = getCurrentTimeRange(currentTime);

    // 🎯 組合完整的中文角色描述（讓AI處理，不要硬編碼判斷）
    const characterDescription = {
      基礎描述: activeProfile.base_prompt || '',
      角色名稱: activeProfile.characterName || '',
      年齡: activeProfile.age || '',
      性別: activeProfile.gender || '',
      職業: activeProfile.occupation || '',
      個性: activeProfile.personality || '',
      聊天風格: activeProfile.chatStyle || '',
      時段情境: getTimeContextDescription(timeRange),
      固定種子: activeProfile.fixed_seed || ''
    };

    // 🎯 使用AI提示詞系統來翻譯和優化整個描述
    const finalPrompt = callAIWithPrompt('REPLY_COST_SAVER_IMAGE_PROMPT_GENERATION', {
      characterDescription: characterDescription,
      timeRange: timeRange,
      originalMessage: event?.message?.text || ''
    });

    console.log(`🎯 AI優化後的提示詞: ${finalPrompt.substring(0, 100)}...`);
    console.log(`🎭 角色資料: ${activeProfile.characterName}(${activeProfile.age}歲${activeProfile.occupation})`);

    return finalPrompt || activeProfile.base_prompt || 'professional photo, natural lighting';

  } catch (error) {
    console.error('❌ 生成情境化提示詞失敗:', error);
    // 備用方案：使用基礎提示詞
    return activeProfile.base_prompt || 'professional photo, natural lighting';
  }
}

/**
 * 🕐 獲取時段情境描述（中文，讓AI翻譯）
 */
function getTimeContextDescription(timeRange) {
  const timeContexts = {
    'lunch': '午餐時光，明亮的日光，在家或台南街頭小吃攤享用美食，人和食物都在畫面中，休閒的日間服裝',
    'dinner': '晚餐時光，溫暖的室內燈光，在家或高級餐廳用餐，人和美食都在畫面中，優雅的晚間服裝',
    'midnight': '深夜時光，溫暖的室內燈光，使用手機或電腦工作，放鬆舒適的表情和姿勢，居家休閒服',
    'default': '日常生活，自然舒適的氛圍'
  };

  return timeContexts[timeRange] || timeContexts['default'];
}

// ===== 🔧 工具函數 =====

/**
 * 📊 獲取發送次數
 */
function getSentCount(keyPrefix, timeRange) {
  try {
    const today = new Date().toDateString();
    const key = `${keyPrefix}_${timeRange}_${today}`;
    const count = PropertiesService.getScriptProperties().getProperty(key);
    return parseInt(count) || 0;
  } catch (error) {
    console.error('獲取發送次數失敗:', error);
    return 0;
  }
}

/**
 * 📈 記錄發送次數
 */
function recordSentCount(keyPrefix, timeRange) {
  try {
    const today = new Date().toDateString();
    const key = `${keyPrefix}_${timeRange}_${today}`;
    const currentCount = getSentCount(keyPrefix, timeRange);
    const newCount = currentCount + 1;

    PropertiesService.getScriptProperties().setProperty(key, newCount.toString());
    console.log(`📈 記錄發送: ${keyPrefix} 在 ${timeRange} 時段第 ${newCount} 次`);

    return newCount;
  } catch (error) {
    console.error('記錄發送次數失敗:', error);
    return 0;
  }
}
