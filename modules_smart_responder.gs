/*
 * 檔案: modules_smart_responder.gs
 * 分類: smart_responder
 * 功能開關: 功能開關_SMART_RESPONDER
 * 描述: AI First 智能回應核心系統
 * 依賴: [modules_smart_responder_utils.gs, modules_ai_prompts.gs, modules_group_tracker.gs, core_utils.gs]
 * 最後更新: 2025-07-06 (v2.1 - 移除硬編碼，完全依賴CHARACTER_PROFILES)
 */

// === 智能回應核心系統 ===
// 🤖 AI First 設計：完全依賴AI判斷對話情境
// 🎯 自然融入群組對話，避免機械式回應
// 🚀 核心功能：分析、判斷、生成、執行

/**
 * 🧠 核心函數：分析群組對話並決定是否回應
 * @param {Object} event - LINE 群組訊息事件
 * @param {string} userId - 發送者用戶ID
 * @param {string} sourceType - 來源類型
 * @returns {Object} 回應決策結果
 */
function analyze_group_conversation(event, userId, sourceType) {
  try {
    console.log(`🤖 開始分析群組對話: 用戶=${userId}, 來源=${sourceType}`);
    
    // 1. 檢查功能開關
    // 🔧 修復：使用更強健的功能開關檢查邏輯
    const rawValue = getConfigValue('功能開關_SMART_RESPONDER', 'APIKEY');
    const isEnabled = rawValue && (
      rawValue === 'TRUE' ||
      rawValue === true ||
      rawValue.toString().trim().toUpperCase() === 'TRUE'
    );

    console.log(`🎛️ 智能回應功能開關: "${rawValue}" → ${isEnabled ? '✅ 啟用' : '❌ 關閉'}`);

    if (!isEnabled) {
      console.log('❌ 智能回應功能已關閉');
      return {
        should_respond: false,
        reason: '功能已關閉',
        response_content: null,
        response_type: null
      };
    }
    
    // 2. 獲取對話上下文
    const conversationContext = get_conversation_context(event);
    
    if (!conversationContext.success) {
      console.log('❌ 無法獲取對話上下文');
      return {
        should_respond: false,
        reason: '無法獲取對話上下文',
        response_content: null,
        response_type: null
      };
    }
    
    // 3. AI 判斷是否需要回應
    const shouldRespond = should_bot_respond(conversationContext.data);
    
    if (!shouldRespond.should_respond) {
      console.log(`🤖 AI 判斷：不需要回應 - ${shouldRespond.reason}`);
      return shouldRespond;
    }
    
    // 4. 生成智能回應
    const smartResponse = generate_smart_response(conversationContext.data, shouldRespond);
    
    console.log(`✅ 智能回應分析完成: 回應類型=${smartResponse.response_type}`);
    
    return {
      should_respond: true,
      reason: shouldRespond.reason,
      response_content: smartResponse.response_content,
      response_type: smartResponse.response_type,
      conversation_context: conversationContext.data
    };
    
  } catch (error) {
    console.error('❌ 智能回應分析失敗:', error);
    return {
      should_respond: false,
      reason: '系統錯誤',
      response_content: null,
      response_type: null,
      error: error.message
    };
  }
}

/**
 * 🎭 檢查角色名稱觸發邏輯
 * 🔧 修復：當智能回應不觸發時，檢查是否提到角色名稱
 * @param {string} messageText - 訊息內容
 * @param {string} userId - 用戶ID
 * @param {string} sourceType - 來源類型
 * @returns {Object} 觸發結果
 */
function checkCharacterNameTrigger(messageText, userId, sourceType) {
  try {
    console.log(`🎭 檢查角色名稱觸發: "${messageText}"`);

    // 1. 獲取啟用的角色設定
    const activeProfile = getActiveCharacterProfile();
    if (!activeProfile) {
      console.log('❌ 沒有啟用的角色設定');
      return {
        should_respond: false,
        reason: '沒有啟用的角色設定',
        response_content: null,
        response_type: null
      };
    }

    const characterName = activeProfile.characterName;
    console.log(`🎭 檢查角色名稱: ${characterName}`);

    // 2. 檢查訊息是否包含角色名稱
    const containsName = messageText.includes(characterName);

    if (!containsName) {
      console.log(`❌ 訊息不包含角色名稱 "${characterName}"`);
      return {
        should_respond: false,
        reason: `訊息不包含角色名稱 "${characterName}"`,
        response_content: null,
        response_type: null
      };
    }

    console.log(`✅ 檢測到角色名稱 "${characterName}"，準備回應`);

    // 3. 生成個性化回應
    const response = callAIWithPrompt('CHARACTER_MENTION_RESPONSE', {
      userMessage: messageText,
      mentionedName: characterName,
      sourceType: sourceType,
      responseType: 'text'
    });

    logActivity('Reply', '角色名稱觸發', 'Success', 'text', 'checkCharacterNameTrigger', `角色: ${characterName}, 訊息: ${messageText.substring(0, 30)}...`);

    return {
      should_respond: true,
      reason: `角色名稱 "${characterName}" 被提及`,
      response_content: response,
      response_type: 'text',
      trigger_type: 'character_mention'
    };

  } catch (error) {
    console.error('❌ 角色名稱觸發檢查失敗:', error);
    logActivity('System', '角色名稱觸發錯誤', 'Failure', 'text', 'checkCharacterNameTrigger', `錯誤: ${error.message}`);

    return {
      should_respond: false,
      reason: `角色名稱觸發檢查失敗: ${error.message}`,
      response_content: null,
      response_type: null
    };
  }
}

/**
 * 🎯 AI 判斷是否需要回應
 * 🔧 v2.1 - 移除硬編碼，完全依賴 CHARACTER_PROFILES
 * @param {Object} conversationContext - 對話上下文
 * @returns {Object} 判斷結果
 */
function should_bot_respond(conversationContext) {
  try {
    console.log('🤖 AI 判斷回應時機...');

    // 🎭 使用個性化群組分析提示詞 - 移除所有硬編碼
    const aiResponse = callAIWithPrompt('GROUP_ANALYSIS_PERSONALIZED', {
      conversation: conversationContext.current_message,
      groupAtmosphere: determineGroupAtmosphere(conversationContext)
      // 🎯 所有人設相關參數將由 CHARACTER_PROFILES 自動提供：
      // - characterName (從 profile_name 映射)
      // - characterBehavior (從 group_behavior 映射)
      // - personality (直接使用)
      // - groupBehavior (從 group_behavior 映射)
      // - forbiddenPhrases (從 forbidden_phrases 映射)
    });
    
    // 解析 AI 回應
    const jsonMatch = aiResponse.match(/\{[\s\S]*\}/);
    if (jsonMatch) {
      const decision = JSON.parse(jsonMatch[0]);
      
      console.log(`🤖 AI 決策: ${decision.should_respond ? '需要回應' : '不需要回應'} (信心度: ${decision.confidence}%)`);
      console.log(`🎯 理由: ${decision.reason}`);
      
      return {
        should_respond: decision.should_respond,
        confidence: decision.confidence,
        reason: decision.reason,
        response_motivation: decision.response_motivation
      };
    }
    
    // 解析失敗，使用保守策略
    console.log('⚠️ AI 回應解析失敗，採用保守策略');
    return {
      should_respond: false,
      confidence: 0,
      reason: 'AI 判斷解析失敗',
      response_motivation: null
    };
    
  } catch (error) {
    console.error('❌ AI 判斷失敗:', error);
    return {
      should_respond: false,
      confidence: 0,
      reason: '判斷系統錯誤',
      response_motivation: null,
      error: error.message
    };
  }
}

/**
 * 🎨 生成智能回應內容
 * @param {Object} conversationContext - 對話上下文
 * @param {Object} respondDecision - 回應決策
 * @returns {Object} 回應內容
 */
function generate_smart_response(conversationContext, respondDecision) {
  try {
    console.log('🎨 生成智能回應內容...');
    
    // 🎭 使用個性化智能回應提示詞 - 移除所有硬編碼
    const aiResponse = callAIWithPrompt('SMART_RESPONSE_PERSONALIZED', {
      userMessage: `${conversationContext.sender_name}: ${conversationContext.current_message}`
      // 🎯 所有人設相關參數將由 CHARACTER_PROFILES 自動提供：
      // - characterName (從 profile_name 映射)
      // - age (直接使用)
      // - occupation (直接使用)
      // - personality (直接使用)
      // - chatStyle (從 chat_style 映射)
      // - responseLength (從 response_length 映射)
      // - formalityLevel (從 formality_level 映射)
      // - emojiUsage (從 emoji_usage 映射)
      // - forbiddenPhrases (從 forbidden_phrases 映射)
    });
    
    // 🎭 個性化回應直接返回文字，需要判斷是否使用語音
    console.log(`🎨 生成個性化回應: ${aiResponse.substring(0, 50)}...`);

    // 簡單的語音判斷邏輯（可以後續優化）
    const shouldUseVoice = determineShouldUseVoice(aiResponse, conversationContext);

    return {
      response_content: aiResponse,
      response_type: shouldUseVoice ? 'voice' : 'text',
      reasoning: '基於人設特色的個性化回應'
    };
    
    // 個性化回應失敗，使用簡單回應
    console.log('⚠️ 個性化回應生成失敗，使用簡單回應');
    return {
      response_content: '我也來參與一下討論 😊',
      response_type: 'text',
      reasoning: '簡單友善的參與'
    };
    
  } catch (error) {
    console.error('❌ 個性化智能回應生成失敗:', error);
    return {
      response_content: '我也想加入討論！',
      response_type: 'text',
      reasoning: '錯誤備用回應',
      error: error.message
    };
  }
}

/**
 * 🎭 判斷群組氛圍
 * @param {Object} conversationContext - 對話上下文
 * @returns {string} 群組氛圍描述
 */
function determineGroupAtmosphere(conversationContext) {
  try {
    const recentMessages = conversationContext.recent_messages || [];
    const currentMessage = conversationContext.current_message || '';

    // 簡單的氛圍判斷邏輯
    if (recentMessages.length === 0) {
      return '安靜的群組';
    }

    if (recentMessages.length >= 3) {
      return '活躍討論中';
    }

    if (/[？?]/.test(currentMessage)) {
      return '有人提問';
    }

    if (/[！!]/.test(currentMessage)) {
      return '熱烈討論';
    }

    return '一般聊天';

  } catch (error) {
    console.error('判斷群組氛圍失敗:', error);
    return '一般聊天';
  }
}

/**
 * 🖼️ 媒體回應決策分析
 * 🔧 v1.6.7 - 新增：讓群組媒體檔案也經過意圖識別
 *
 * 📋 支援的回覆鏈：
 * 🔵 被動回覆鏈：用戶上傳圖片 → 意圖識別 → 智能回應
 * 🟡 偽主動回覆鏈：Reply成本節省 → 搭便車媒體回覆
 *
 * @param {Object} event - LINE 媒體訊息事件
 * @param {string} userId - 用戶ID
 * @param {string} sourceType - 來源類型
 * @returns {Object} 媒體回應決策結果
 */
function analyze_media_response_decision(event, userId, sourceType) {
  try {
    console.log(`🖼️ 分析媒體回應決策: 類型=${event.message?.type}, 用戶=${userId}`);

    // 🟡 步驟1：檢查Reply成本節省（偽主動回覆鏈）
    console.log(`🟡 檢查Reply成本節省機會...`);

    // 🔧 修復：直接檢查Reply成本節省，避免完整的智能分析
    const replyCostSaverCheck = check_reply_cost_saver_opportunity(event);

    if (replyCostSaverCheck.canSaveReplyFee) {
      console.log(`🟡 Reply成本節省可行，生成偽主動回覆...`);
      const saverResponse = generate_reply_cost_saver_response('IMAGE', replyCostSaverCheck, event);

      if (saverResponse.should_respond && saverResponse.reply_cost_saver_type) {
        console.log(`🟡 偽主動媒體回覆觸發: ${saverResponse.reason}`);
        return {
          should_respond: true,
          reason: `偽主動回覆: ${saverResponse.reason}`,
          response_type: 'cost_saver',
          cost_saver_type: saverResponse.reply_cost_saver_type,
          cost_saver_data: saverResponse
        };
      }
    }

    // 記錄Reply成本節省檢查結果
    logActivity('System', 'Reply成本節省媒體檢查', replyCostSaverCheck.canSaveReplyFee ? 'Success' : 'Failure',
                'saver', 'analyze_media_response_decision',
                `媒體Reply成本節省: ${replyCostSaverCheck.canSaveReplyFee ? '可行' : '不可行'}, 原因: ${replyCostSaverCheck.reason}`);

    // 🔵 步驟2：被動回覆鏈 - 媒體意圖識別（僅在Reply成本節省不可行時執行）
    console.log(`🔵 Reply成本節省不可行，檢查被動媒體回覆意圖...`);

    // 🎯 目前專注於圖片，其他媒體類型使用佔位符
    if (event.message?.type === 'image') {
      // 🖼️ 圖片意圖分析：檢查是否包含生圖意圖
      const imageIntent = analyze_image_upload_intent(event, userId, sourceType);

      if (imageIntent.should_respond) {
        console.log(`🔵 被動圖片回覆觸發: ${imageIntent.reason}`);
        return {
          should_respond: true,
          reason: `被動回覆: ${imageIntent.reason}`,
          response_type: 'passive_image',
          intent_data: imageIntent
        };
      }
    } else {
      // 🔲 其他媒體類型佔位符
      console.log(`🔲 媒體類型 ${event.message?.type} 暫不支援智能回應（佔位符）`);
    }

    // 🔲 步驟3：無回應意圖，靜默處理
    const finalReason = replyCostSaverCheck.canSaveReplyFee ? '無回應意圖，靜默處理' : `Reply成本節省不可行(${replyCostSaverCheck.reason})且無被動回應意圖`;
    return {
      should_respond: false,
      reason: finalReason,
      response_type: null
    };

  } catch (error) {
    console.error('媒體回應決策分析失敗:', error);
    return {
      should_respond: false,
      reason: `分析失敗: ${error.message}`,
      response_type: null
    };
  }
}

/**
 * 🖼️ 圖片上傳意圖分析
 * 🔵 被動回覆鏈專用：分析用戶上傳圖片的意圖
 *
 * @param {Object} event - LINE 圖片訊息事件
 * @param {string} userId - 用戶ID
 * @param {string} sourceType - 來源類型
 * @returns {Object} 圖片意圖分析結果
 */
function analyze_image_upload_intent(event, userId, sourceType) {
  try {
    console.log(`🖼️ 分析圖片上傳意圖...`);

    // 🔍 步驟1：獲取圖片描述（重用已分析的結果，避免重複下載）
    console.log(`📸 步驟1：獲取圖片內容描述...`);

    // 🔧 修復：先嘗試從已處理的媒體中獲取描述，避免重複下載
    let imageDescription = null;
    try {
      // 簡化版圖片分析：直接調用vision模型
      const config = getConfig();
      if (!config.geminiApiKey) {
        return {
          should_respond: false,
          reason: '缺少Gemini API Key',
          intent_type: 'config_error',
          confidence: 0
        };
      }

      // 下載並分析圖片
      const imageBlob = UrlFetchApp.fetch(`https://api-data.line.me/v2/bot/message/${event.message.id}/content`, {
        headers: {
          Authorization: 'Bearer ' + config.lineChannelAccessToken
        }
      }).getBlob();

      imageDescription = callGemini("請用繁體中文詳細描述這張圖片的內容，包括：主要物件、場景、文字、顏色、人物動作、情感氛圍等。如果圖片包含文字，請完整提取。", 'vision', imageBlob);
      console.log(`✅ 分析圖片後有文字: "${imageDescription}"`);

    } catch (analysisError) {
      console.error('圖片分析失敗:', analysisError);
      return {
        should_respond: false,
        reason: `圖片分析失敗: ${analysisError.message}`,
        intent_type: 'image_analysis_failed',
        confidence: 0
      };
    }

    // 🧠 步驟2：基於圖片文字描述進行意圖分析
    console.log(`🧠 步驟2：基於圖片文字進行意圖識別...`);
    const textIntent = analyzeUserIntentWithAI(imageDescription, sourceType, userId);

    console.log(`🔍 圖片文字意圖分析結果: ${textIntent.primary_intent} (信心度: ${textIntent.confidence}%)`);

    // 🎯 步驟3：判斷是否應該回應
    const shouldRespond = shouldRespondToImageIntent(textIntent, imageDescription);

    if (shouldRespond.should_respond) {
      console.log(`✅ 圖片觸發被動回應: ${shouldRespond.reason}`);
      return {
        should_respond: true,
        reason: `被動回覆: ${shouldRespond.reason}`,
        intent_type: 'image_content_response',
        confidence: textIntent.confidence,
        image_description: imageDescription,
        text_intent: textIntent,
        response_type: shouldRespond.response_type
      };
    } else {
      console.log(`📝 圖片無須回應: ${shouldRespond.reason}`);
      return {
        should_respond: false,
        reason: shouldRespond.reason,
        intent_type: 'image_upload',
        confidence: textIntent.confidence,
        image_description: imageDescription,
        text_intent: textIntent
      };
    }

  } catch (error) {
    console.error('圖片意圖分析失敗:', error);
    return {
      should_respond: false,
      reason: `分析失敗: ${error.message}`,
      intent_type: 'unknown',
      confidence: 0
    };
  }
}

/**
 * 🟡 執行偽主動媒體回覆
 * 🔲 佔位符函數：處理偽主動媒體回覆邏輯
 *
 * @param {Object} mediaResponseDecision - 媒體回應決策結果
 * @param {string} replyToken - 回覆Token
 */
function execute_cost_saver_media_response(mediaResponseDecision, replyToken) {
  try {
    console.log(`🟡 執行偽主動媒體回覆: ${mediaResponseDecision.cost_saver_type}`);

    const costSaverData = mediaResponseDecision.cost_saver_data;

    switch (costSaverData.response_type) {
      case 'image':
        console.log(`🖼️ 執行偽主動圖片回覆...`);
        execute_reply_cost_saver_response(costSaverData, replyToken);
        break;

      case 'text':
        console.log(`📝 執行偽主動文字回覆（佔位符）...`);
        // 🔲 佔位符：偽主動文字回覆
        break;

      case 'audio':
        console.log(`🔊 執行偽主動語音回覆（佔位符）...`);
        // 🔲 佔位符：偽主動語音回覆
        break;

      case 'video':
        console.log(`🎬 執行偽主動影片回覆（佔位符）...`);
        // 🔲 佔位符：偽主動影片回覆
        break;

      default:
        console.log(`❓ 未知的偽主動回覆類型: ${costSaverData.response_type}`);
    }

  } catch (error) {
    console.error('執行偽主動媒體回覆失敗:', error);
  }
}

/**
 * 🔊 判斷是否應該使用語音回應
 * @param {string} responseContent - 回應內容
 * @param {Object} conversationContext - 對話上下文
 * @returns {boolean} 是否使用語音
 */
function determineShouldUseVoice(responseContent, conversationContext) {
  try {
    // 簡短回應不使用語音
    if (responseContent.length < 20) {
      return false;
    }

    // 包含連結或特殊符號不使用語音
    if (/http|www\.|@|#/.test(responseContent)) {
      return false;
    }

    // 情感支持類回應優先使用語音
    if (/加油|支持|鼓勵|安慰|理解/.test(responseContent)) {
      return true;
    }

    // ❌ 移除硬編碼長度判斷 - 改為基於場景和用戶偏好
    // if (responseContent.length > 100) {
    //   return true;
    // }

    // 預設不使用語音
    return false;

  } catch (error) {
    console.error('判斷語音使用失敗:', error);
    return false;
  }
}

/**
 * 🚀 執行智能回應
 * @param {string} replyToken - LINE 回覆 Token
 * @param {Object} responseData - 回應數據
 */
function execute_smart_response(replyToken, responseData) {
  try {
    console.log(`🚀 執行智能回應: ${responseData.response_type}`);
    
    if (responseData.response_type === 'voice') {
      // 語音回應
      console.log('🔊 生成語音回應...');
      
      // 調用語音合成功能
      const ttsResult = callGeminiTTS(responseData.response_content);
      
      if (ttsResult.success) {
        replyWithAudio(replyToken, ttsResult, responseData.response_content);
        console.log('✅ 語音回應發送成功');
      } else {
        // 語音失敗，改用文字
        replyMessage(replyToken, responseData.response_content);
        console.log('⚠️ 語音回應失敗，改用文字回應');
      }
      
    } else {
      // 文字回應
      replyMessage(replyToken, responseData.response_content);
      console.log('✅ 文字回應發送成功');
    }
    
    // 記錄智能回應活動
    logActivity(
      'SmartResponse', 
      '智能回應執行', 
      'Success', 
      responseData.response_type, 
      'execute_smart_response',
      `回應內容: ${responseData.response_content.substring(0, 50)}...`
    );
    
  } catch (error) {
    console.error('❌ 執行智能回應失敗:', error);
    
    // 記錄錯誤
    logActivity(
      'SmartResponse', 
      '智能回應失敗', 
      'Failure', 
      responseData.response_type, 
      'execute_smart_response',
      `錯誤: ${error.message}`
    );
    
    // 嘗試發送簡單文字回應
    try {
      replyMessage(replyToken, '我想參與討論，但出現了技術問題 😅');
    } catch (fallbackError) {
      console.error('連備用回應也失敗:', fallbackError);
    }
  }
}

/**
 * 📸 分析用戶上傳的圖片內容
 * 🔵 被動回覆鏈專用：獲取圖片的文字描述
 *
 * @param {Object} message - LINE 圖片訊息物件
 * @returns {Object} 圖片分析結果
 */
function analyzeUploadedImageContent(message) {
  try {
    console.log(`📸 開始分析上傳圖片內容...`);

    const config = getConfig();
    if (!config.geminiApiKey) {
      return {
        success: false,
        error: '缺少Gemini API Key'
      };
    }

    // 下載圖片
    const imageBlob = UrlFetchApp.fetch(`https://api-data.line.me/v2/bot/message/${message.id}/content`, {
      headers: {
        Authorization: 'Bearer ' + config.lineChannelAccessToken
      }
    }).getBlob();

    // 使用vision模型分析圖片
    const imageDescription = callGemini("請用繁體中文詳細描述這張圖片的內容，包括：主要物件、場景、文字、顏色、人物動作、情感氛圍等。如果圖片包含文字，請完整提取。", 'vision', imageBlob);

    console.log(`✅ 圖片內容分析完成，描述長度: ${imageDescription.length}`);

    return {
      success: true,
      description: imageDescription,
      messageId: message.id
    };

  } catch (error) {
    console.error('圖片內容分析失敗:', error);
    return {
      success: false,
      error: error.message
    };
  }
}

/**
 * 🎯 判斷圖片意圖是否應該觸發回應
 * 🔵 被動回覆鏈專用：決定什麼樣的圖片會觸發非靜默處理
 *
 * @param {Object} textIntent - 基於圖片文字的意圖分析結果
 * @param {string} imageDescription - 圖片描述文字
 * @returns {Object} 回應決策結果
 */
function shouldRespondToImageIntent(textIntent, imageDescription) {
  try {
    console.log(`🎯 判斷圖片是否應該觸發回應...`);

    // 🔥 高優先級：明確的互動意圖
    const highPriorityIntents = [
      'image_generation',     // 圖片生成請求
      'question_answer',      // 問答需求
      'help_request',         // 求助請求
      'creative_request',     // 創意請求
      'analysis_request'      // 分析請求
    ];

    if (highPriorityIntents.includes(textIntent.primary_intent) && textIntent.confidence >= 70) {
      return {
        should_respond: true,
        reason: `高優先級意圖: ${textIntent.primary_intent} (信心度: ${textIntent.confidence}%)`,
        response_type: 'high_priority_response'
      };
    }

    // 🎨 圖片內容觸發：包含特定關鍵詞或場景
    const triggerKeywords = [
      '問題', '疑問', '幫助', '解釋', '說明',
      '這是什麼', '怎麼', '為什麼', '如何',
      '生成', '畫', '創作', '設計',
      '分析', '評價', '意見', '建議'
    ];

    const hasKeywords = triggerKeywords.some(keyword => imageDescription.includes(keyword));
    if (hasKeywords) {
      return {
        should_respond: true,
        reason: `圖片內容包含觸發關鍵詞，描述: "${imageDescription.substring(0, 50)}..."`,
        response_type: 'keyword_triggered_response'
      };
    }

    // 📝 文字內容觸發：圖片包含大量文字
    if (imageDescription.length > 100 && textIntent.confidence >= 60) {
      return {
        should_respond: true,
        reason: `圖片包含豐富內容且有中等信心度意圖: ${textIntent.primary_intent}`,
        response_type: 'content_rich_response'
      };
    }

    // 🤔 特殊場景觸發
    const specialScenes = [
      '螢幕截圖', '錯誤訊息', '程式碼', '文件', '表格',
      '問卷', '表單', '菜單', '價格', '時間表'
    ];

    const hasSpecialScene = specialScenes.some(scene => imageDescription.includes(scene));
    if (hasSpecialScene) {
      return {
        should_respond: true,
        reason: `圖片包含特殊場景，可能需要協助: "${imageDescription.substring(0, 50)}..."`,
        response_type: 'special_scene_response'
      };
    }

    // 🔲 預設：靜默處理
    return {
      should_respond: false,
      reason: `圖片內容無明確互動意圖 (意圖: ${textIntent.primary_intent}, 信心度: ${textIntent.confidence}%)`,
      response_type: null
    };

  } catch (error) {
    console.error('圖片回應判斷失敗:', error);
    return {
      should_respond: false,
      reason: `判斷失敗: ${error.message}`,
      response_type: null
    };
  }
}
