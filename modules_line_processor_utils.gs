// TextProcessor_Utils.gs
// == 工具函數模組 ==
// 從 TextProcessor_AIFirst.gs 拆分
// 📁 職責：測試函數、調試工具、輔助功能

// 🧪 測試核心流程
function testAIFirstCoreV14_debug() {
  console.log('=== 測試 AI-First 核心流程 ===');
  
  const testMessages = [
    '測試',                      // 應該被預檢測捕獲
    'help',                     // 應該被預檢測捕獲  
    '畫一隻貓',                  // 應該被 AI 分析為圖片生成
    '你說 Hello World',         // 應該被 AI 分析為 TTS
    '跟我聊聊天氣',              // 應該被 AI 分析為對話音頻
    '這是什麼意思？',            // 應該被 AI 分析為一般問題
    '！故事接龍 然後貓咪跑走了'   // 應該被預檢測捕獲
  ];
  
  const testUserId = 'test_user_debug';
  const testSourceType = 'user';
  
  testMessages.forEach((message, index) => {
    console.log(`\n--- 測試 ${index + 1}: "${message}" ---`);
    
    try {
      // 測試預檢測
      const systemCheck = preDetectSystemCommand(message);
      console.log(`🛡️ 預檢測結果: ${JSON.stringify(systemCheck)}`);
      
      if (!systemCheck.isSystemCommand) {
        // 測試 AI 意圖分析
        const intent = analyzeUserIntentWithAI(message, testSourceType, testUserId);
        console.log(`🧠 AI 意圖分析: ${JSON.stringify(intent)}`);
        
        // 測試智能引導檢查
        const needsGuidance = shouldTriggerGuidance(intent);
        console.log(`🤔 需要智能引導: ${needsGuidance}`);
      }
      
    } catch (error) {
      console.error(`❌ 測試 ${index + 1} 失敗:`, error);
    }
  });
  
  console.log('\n=== AI-First 核心流程測試完成 ===');
}

/**
 * 🧪 測試延續故事狀態流程整合
 */
function testContinueStoryIntegration_debug() {
  console.log('🧪 === 測試延續故事狀態流程整合 ===');
  
  try {
    const testUserId = 'test-integration-user';
    
    // 1. 測試檢查延續故事狀態（應該返回 null）
    console.log('\n1. 測試檢查延續故事狀態');
    const storyState = checkContinueStoryState(testUserId, '繼續畫一隻狗');
    console.log(`🎪 延續故事狀態: ${JSON.stringify(storyState)}`);
    
    // 2. 測試 AI-First 主函數處理
    console.log('\n2. 測試 AI-First 主函數');
    const testMessages = [
      '畫一隻貓咪在花園裡',
      '繼續故事 然後貓咪遇到了一隻小鳥',
      '！故事接龍 小鳥和貓咪成為了朋友'
    ];
    
    testMessages.forEach((message, index) => {
      console.log(`\n--- 測試訊息 ${index + 1}: "${message}" ---`);
      
      try {
        // 模擬處理（不實際發送）
        console.log(`📝 模擬處理: ${message}`);
        
        // 檢查預檢測
        const systemCheck = preDetectSystemCommand(message);
        if (systemCheck.isSystemCommand) {
          console.log(`🛡️ 預檢測命中: ${systemCheck.commandType}`);
        } else {
          console.log(`🧠 將進行 AI 意圖分析`);
        }
        
      } catch (error) {
        console.error(`❌ 處理訊息失敗:`, error);
      }
    });
    
  } catch (error) {
    console.error('🧪 延續故事整合測試失敗:', error);
  }
  
  console.log('\n🧪 === 延續故事狀態流程整合測試完成 ===');
}

/**
 * 🧪 測試故事接龍功能
 */
function testStoryChain_debug() {
  console.log('🧪 === 測試故事接龍功能 ===');

  const testUserId = 'test_user_123';

  // 1. 測試記錄圖片歷史
  console.log('\n1. 測試圖片歷史記錄');
  try {
    // 模擬記錄一個圖片歷史
    const mockImageData = {
      originalPrompt: '一隻可愛的小貓在花園裡玩耍',
      seed: 123456,
      timestamp: new Date().toISOString()
    };
    
    console.log(`📸 模擬記錄圖片歷史: ${JSON.stringify(mockImageData)}`);
    // recordImageHistory(testUserId, mockImageData); // 實際使用時取消註釋
    
  } catch (error) {
    console.error('❌ 記錄圖片歷史失敗:', error);
  }

  // 2. 測試故事接龍指令
  console.log('\n2. 測試故事接龍指令');
  const storyCommands = [
    '！故事接龍 然後小貓遇到了一隻小狗',
    '故事接龍 牠們一起在草地上奔跑',
    '！故事接龍'  // 無內容測試
  ];

  storyCommands.forEach((command, index) => {
    console.log(`\n--- 故事接龍測試 ${index + 1}: "${command}" ---`);
    
    try {
      const systemCheck = preDetectSystemCommand(command);
      console.log(`🛡️ 預檢測結果: ${JSON.stringify(systemCheck)}`);
      
      if (systemCheck.isSystemCommand && systemCheck.commandType === 'story_chain') {
        console.log(`🎪 故事接龍指令確認，將調用處理函數`);
        // handleStoryChainCommand(command, null, testUserId, 'user', {}); // 實際使用時取消註釋
      }
      
    } catch (error) {
      console.error(`❌ 故事接龍測試失敗:`, error);
    }
  });

  console.log('\n🧪 === 故事接龍功能測試完成 ===');
}

/**
 * 🧪 測試明確指令檢測修復
 */
function testExplicitCommandFix_debug() {
  console.log('🧪 === 測試明確指令檢測修復 ===');

  const testCommands = [
    '繼續故事 這位工程師她拿了一碗麵放在螢幕前面',
    '！繼續故事 他開始吃麵',
    '故事接龍 麵條很香很好吃',
    '！故事接龍 工程師很滿足',
    'test',
    'help',
    '測試'
  ];

  testCommands.forEach((command, index) => {
    console.log(`\n--- 指令測試 ${index + 1}: "${command}" ---`);
    
    try {
      const result = preDetectSystemCommand(command);
      console.log(`🛡️ 預檢測結果: ${JSON.stringify(result)}`);
      
      if (result.isSystemCommand) {
        console.log(`✅ 成功檢測到系統命令: ${result.commandType}`);
      } else {
        console.log(`🧠 將交由 AI 分析處理`);
      }
      
    } catch (error) {
      console.error(`❌ 指令檢測失敗:`, error);
    }
  });

  console.log('\n🧪 === 明確指令檢測修復測試完成 ===');
}

/**
 * 🧪 測試故事延續功能修復
 */
function testStoryContinuationFix_debug() {
  console.log('🧪 === 測試故事延續功能修復 ===');

  try {
    const testUserId = 'test_user_123';
    const testSeed = 616863;
    const testPrompt = '一位工程師坐在電腦前面寫程式';

    // 1. 測試 v1.6.2 故事等待狀態檢查
    console.log('\n1. 測試故事等待狀態檢查');
    const waitingState = checkStoryWaitingState(testUserId);
    console.log(`🎬 等待狀態: ${JSON.stringify(waitingState)}`);

    // 2. 測試最近故事狀態查找
    console.log('\n2. 測試最近故事狀態查找');
    const recentState = findRecentStoryState(testUserId);
    console.log(`🔍 最近狀態: ${JSON.stringify(recentState)}`);

    // 3. 測試故事延續輸入處理
    console.log('\n3. 測試故事延續輸入處理');
    if (waitingState) {
      console.log(`🎬 模擬處理故事延續輸入`);
      // handleStoryInputProcessing(waitingState, '然後他喝了一口咖啡', null, testUserId, 'user', {}); // 實際使用時取消註釋
    } else {
      console.log(`🎬 無等待狀態，跳過延續輸入測試`);
    }

    console.log('\n✅ 故事延續功能修復測試完成');

  } catch (error) {
    console.error('❌ 故事延續功能測試失敗:', error);
  }
}
