// TextProcessor_Core.gs
// == 核心處理模組 ==
// 🔧 v1.5.2 - 從 TextProcessor_AIFirst.gs 拆分
// 📁 職責：主要處理函數、AI路由、特殊回應處理
// 📅 最後更新: 2025-01-09 - 修復 image_response_async 處理和 replyToken 傳遞

/**
 * 🧠 AI-First 文字訊息處理主函數
 * 完全依賴 Gemini AI 理解用戶意圖，不使用命令匹配
 * 🆕 v1.4.0 - 新增延續故事狀態檢查支援
 */
function handleTextMessageAIFirst(text, replyToken, userId, sourceType) {
  try {
    console.log(`📝 收到文字訊息: ${text} (用戶: ${userId}, 來源: ${sourceType})`);
    
    // 0. 🆕 v1.4.0：優先檢查延續故事狀態
    // 注意：checkContinueStoryState 函數定義在 Webhook.gs 中
    const continueStoryState = checkContinueStoryState(userId, text);
    if (continueStoryState) {
      console.log(`🎪 檢測到延續故事狀態，直接處理圖片生成`);
      return handleContinueStoryGeneration(continueStoryState, replyToken, userId, sourceType);
    }

    // 1. 🛡️ 預檢測系統命令（100% 準確率優先）
    const systemCheck = preDetectSystemCommand(text);
    if (systemCheck.isSystemCommand) {
      console.log(`🛡️ 預檢測到系統命令: ${systemCheck.commandType}`);
      return handleSystemCommandDirect(systemCheck.commandType, sourceType, replyToken, text, userId);
    }

    // 2. 🧠 使用 AI 分析用戶意圖
    const userIntent = analyzeUserIntentWithAI(text, sourceType, userId);
    
    // 3. 🎯 基於 AI 意圖進行智能路由
    const response = routeByAIIntent(userIntent, text, userId, sourceType, replyToken);
    
    // 4. 📤 發送回應
    if (replyToken && response) {
      if (typeof response === 'object' && response.type) {
        // 特殊回應類型（音頻、圖片等）
        handleSpecialResponseV2(response, replyToken, text, userId, sourceType, userIntent);
      } else {
        // 一般文字回應
        replyMessage(replyToken, response);
        logActivity('Reply', 'AI-First處理', 'Success', 'text', 'handleTextMessageAIFirst', `意圖: ${userIntent.primary_intent}`);
      }
    }
    
    return response;
    
  } catch (error) {
    console.error('AI-First 處理錯誤:', error);
    return handleAIProcessingError(text, replyToken, userId, sourceType, error);
  }
}

/**
 * 🎯 基於 AI 意圖的智能路由系統
 * 完全取代硬編碼的 if-else 命令匹配
 * 🔧 v1.4.0 - 支援延續故事模式的圖片生成
 * 🚨 v1.6.8 - 添加 replyToken 參數支援異步圖片生成
 */
function routeByAIIntent(intent, originalMessage, userId, sourceType, replyToken = null) {
  try {
    console.log(`🎯 路由到意圖: ${intent.primary_intent} (信心度: ${intent.confidence}%)`);
    
    // 🚀 智能引導檢查 - 當 AI 信心度過低時提供統一引導
    if (shouldTriggerGuidance(intent)) {
      console.log(`🤔 觸發智能引導 (信心度: ${intent.confidence}%)`);
      return callAIWithPrompt('UNIFIED_GUIDANCE', {});
    }
    
    // 🔗 委託給專門處理函數 (在 AIHandlers_Specialized.gs 中)
    switch (intent.primary_intent) {
      case 'drive_link_sharing':
        return handleGoogleDriveLink(originalMessage, userId, sourceType);
        
      case 'note_taking':
        // 🎛️ 檢查筆記功能是否啟用
        const noteFeatureCheck = checkContentFeatureEnabled('NOTE_TAKING');
        if (!noteFeatureCheck.enabled) {
          return noteFeatureCheck.message;
        }
        return handleAINoteRequest(intent, originalMessage, userId, sourceType);

      case 'file_query':
        // 🎛️ 檢查檔案查詢功能是否啟用
        const fileFeatureCheck = checkContentFeatureEnabled('FILE_QUERY');
        if (!fileFeatureCheck.enabled) {
          return fileFeatureCheck.message;
        }
        // 🔧 修復：使用現有的檔案查詢函數
        return handleFileReference(userId, originalMessage, sourceType);

      case 'conversation_review':
        // 🎛️ 檢查對話回顧功能是否啟用
        const reviewFeatureCheck = checkContentFeatureEnabled('CONVERSATION_REVIEW');
        if (!reviewFeatureCheck.enabled) {
          return reviewFeatureCheck.message;
        }
        return handleAIConversationReview(intent, userId, sourceType);
        
      case 'group_member_query':
        if (sourceType === 'group' || sourceType === 'room') {
          // 🔧 修復：使用正確的函數名稱
          return handleSmartGroupQuery(originalMessage, userId, sourceType);
        } else {
          return generateAIResponse(intent, '這個功能只能在群組中使用。您想要查看我們的對話記錄嗎？');
        }
        
      case 'social_media_post':
        return handleAISocialMediaPost(intent, originalMessage, userId, sourceType);

      case 'text_to_speech':
        // 🎛️ 檢查TTS功能是否啟用
        const ttsFeatureCheck = checkAudioFeatureEnabled('TEXT_TO_SPEECH');
        if (!ttsFeatureCheck.enabled) {
          return ttsFeatureCheck.message;
        }
        return handleAITTSRequest(intent, originalMessage, userId, sourceType);

      case 'conversational_audio':
      case 'voice_chat':
        // 🎛️ 檢查對話音頻功能是否啟用
        const audioFeatureCheck = checkAudioFeatureEnabled('CONVERSATIONAL_AUDIO');
        if (!audioFeatureCheck.enabled) {
          return audioFeatureCheck.message;
        }
        return handleAIConversationalAudio(intent, originalMessage, userId, sourceType);

      case 'image_generation':
        // 🎛️ 檢查圖片功能是否啟用
        const imageFeatureCheck = checkImageFeatureEnabled();
        if (!imageFeatureCheck.enabled) {
          return imageFeatureCheck.message;
        }

        // 🔧 v1.4.3 修復：傳遞目標信息到圖片生成函數（包含 replyToken）
        const targetInfo = {
          groupId: intent.groupId || null,
          roomId: intent.roomId || null,
          replyToken: replyToken  // 🚨 修復：添加 replyToken 以支援異步圖片生成
        };
        return handleAIImageGeneration_PassiveChain(intent, originalMessage, userId, sourceType, targetInfo);

      case 'system_help':
        return generateAIHelpResponse(intent, sourceType);

      case 'system_command':
        return handleAISystemCommand(intent, originalMessage, sourceType);

      case 'model_examples':
        return generateModelExamplesResponse(intent, sourceType);

      case 'general_question':
        // 🔧 修復：檢查是否為被錯誤分類的語音聊天請求
        if (/跟我聊|聊聊|聊天|對話|語音聊天|語音對話|用語音|語音回應/i.test(originalMessage)) {
          console.log(`🔧 修復：將 general_question 重新路由到語音對話`);
          return handleAIConversationalAudio(intent, originalMessage, userId, sourceType);
        }
        return handleAIGeneralQuestion(intent, originalMessage, sourceType);

      case 'casual_chat':
        // 🔧 修復：檢查是否為被錯誤分類的語音聊天請求
        if (/跟我聊|聊聊|聊天|對話|語音聊天|語音對話|用語音|語音回應/i.test(originalMessage)) {
          console.log(`🔧 修復：將 casual_chat 重新路由到語音對話`);
          return handleAIConversationalAudio(intent, originalMessage, userId, sourceType);
        }

        // 🔧 處理閒聊回應，支援前綴驚嘆號檢查
        const casualResponse = generateAICasualResponse(intent, originalMessage);
        if (casualResponse === null) {
          // 沒有前綴驚嘆號且處理失敗，不回應
          console.log('❌ 閒聊處理失敗且無前綴驚嘆號，不回應');
          return null;
        }
        return casualResponse;

      default:
        // 🤖 如果不確定意圖，讓 AI 自主決定如何回應
        return handleAIUncertainIntent(intent, originalMessage, sourceType);
    }

  } catch (error) {
    console.error('AI 路由錯誤:', error);
    return generateAIErrorResponse(intent, originalMessage, error);
  }
}

/**
 * 🤖 處理不確定意圖
 * 🔧 修復：創建缺失的函數，避免 "handleAIUncertainIntent is not defined" 錯誤
 */
function handleAIUncertainIntent(intent, originalMessage, sourceType) {
  try {
    console.log(`🤖 處理不確定意圖: ${intent.primary_intent} (信心度: ${intent.confidence}%)`);

    // 🎯 使用智能回應系統處理不確定的意圖
    const response = callAIWithPrompt('SMART_RESPONSE_PERSONALIZED', {
      userMessage: originalMessage,
      sourceType: sourceType,
      responseType: 'text',
      intentType: intent.primary_intent || 'uncertain'
    });

    logActivity('Reply', 'AI不確定意圖處理', 'Success', 'text', 'handleAIUncertainIntent',
                `意圖: ${intent.primary_intent}, 信心度: ${intent.confidence}%, 訊息: ${originalMessage.substring(0, 50)}...`);

    return response;

  } catch (error) {
    console.error('處理不確定意圖失敗:', error);
    return '我理解您的訊息，但目前無法確定最佳的回應方式。請您換個方式表達，或者告訴我您需要什麼幫助？';
  }
}

/**
 * 🎯 AI-First 文字處理器（帶目標信息版本）v1.4.3
 * 修復群組圖片生成回復到個人窗口的問題
 */
function handleTextMessageAIFirstWithTarget(text, replyToken, userId, sourceType, targetInfo = {}) {
  try {
    console.log(`🚀 AI-First 處理文字訊息（帶目標信息）: ${text.substring(0, 50)}...`);
    console.log(`🎯 目標信息: ${JSON.stringify(targetInfo)}`);

    // 0. 🆕 v1.6.2：檢查故事延續狀態（新版）
    const storyWaitingState = checkStoryWaitingState(userId);
    if (storyWaitingState) {
      console.log(`🎬 [v1.6.2] 檢測到故事延續狀態，處理用戶輸入`);
      return handleStoryInputProcessing(storyWaitingState, text, replyToken, userId, sourceType, targetInfo);
    }

    // 1. 🛡️ 預檢測系統命令（100% 準確率優先）
    const systemCheck = preDetectSystemCommand(text);
    if (systemCheck.isSystemCommand) {
      console.log(`🛡️ 預檢測到系統命令: ${systemCheck.commandType}`);
      return handleSystemCommandDirect(systemCheck.commandType, sourceType, replyToken, text, userId, targetInfo);
    }

    // 2. 🧠 使用 AI 分析用戶意圖
    const userIntent = analyzeUserIntentWithAI(text, sourceType, userId);

    // 🔧 v1.4.3 修復：將目標信息注入到意圖對象中
    userIntent.groupId = targetInfo.groupId || null;
    userIntent.roomId = targetInfo.roomId || null;

    // 3. 🎯 基於 AI 意圖進行智能路由
    const response = routeByAIIntent(userIntent, text, userId, sourceType, replyToken);

    // 4. 📤 發送回應
    if (replyToken && response) {
      if (typeof response === 'object' && response.type) {
        // 特殊回應類型（音頻、圖片等）
        handleSpecialResponseV2(response, replyToken, text, userId, sourceType, userIntent);
      } else {
        // 一般文字回應
        replyMessage(replyToken, response);
        logActivity('Reply', 'AI-First處理（帶目標）', 'Success', 'text', 'handleTextMessageAIFirstWithTarget', `意圖: ${userIntent.primary_intent}, 目標: ${JSON.stringify(targetInfo)}`);
      }
    }

    return response;

  } catch (error) {
    console.error('AI-First 處理錯誤（帶目標信息）:', error);
    return handleAIProcessingError(text, replyToken, userId, sourceType, error);
  }
}

/**
 * 🎯 AI-First 特殊回應處理器 v2.0
 * 處理音頻、圖片、異步圖片等特殊回應類型
 * 🔧 修復群組圖片生成回復到個人窗口的問題
 */
function handleSpecialResponseV2(response, replyToken, originalText, userId, sourceType, userIntent) {
  try {
    console.log(`🎯 處理特殊回應類型: ${response.type}`);

    // 🔧 構建完整的目標信息（修復群組發送問題 + 錯誤處理支援）
    const targetInfo = {
      userId: userId,
      sourceType: sourceType,
      groupId: (userIntent && userIntent.groupId) || null,
      roomId: (userIntent && userIntent.roomId) || null
    };

    console.log(`🎯 目標信息: ${JSON.stringify(targetInfo)}`);

    switch (response.type) {
      case 'audio_response':
        console.log('🔊 處理音頻回應');
        if (replyToken) {
          replyWithAudio(replyToken, response.audioResult, response.originalText);
          logActivity('Reply', '音頻回應', 'Success', 'audio', 'handleSpecialResponseV2', `用戶: ${userId}(${sourceType})`);
        } else {
          // 備用：使用 Push API
          pushAudioMessageV143(targetInfo, response.audioResult);
          logActivity('Push', '音頻回應', 'Success', 'audio', 'handleSpecialResponseV2', `用戶: ${userId}(${sourceType})`);
        }
        break;

      case 'image_response':
        console.log('🎨 處理圖片回應（同步模式）');
        // 🎨 同步圖片生成：立即生成並回覆
        try {
          const imageResult = generateImageWithGemini(response.imagePrompt);

          if (imageResult.success) {
            // 🆕 v1.6.8 修復：被動調用鏈也生成配圖故事
            let imageStory = null;
            try {
              console.log(`📖 [被動鏈] 開始生成配圖故事...`);
              imageStory = generateImageStory(response.imagePrompt);
              console.log(`✅ [被動鏈] 配圖故事生成完成，內容: ${imageStory ? imageStory.substring(0, 50) + '...' : 'null'}`);
            } catch (storyError) {
              console.error('⚠️ [被動鏈] 配圖故事生成失敗，但不影響主功能:', storyError);
              imageStory = null;
            }

            if (replyToken) {
              replyWithImage(replyToken, imageResult, response.originalMessage, userId, imageStory);  // 🔧 修復：傳遞 imageStory
              logActivity('Reply', '圖片回應', 'Success', 'image', 'handleSpecialResponseV2', `用戶: ${userId}(${sourceType}), 配額節省=✅, 故事=${imageStory ? '有' : '無'}`);
            } else {
              // 備用：使用 Push API
              pushImageResultV143(targetInfo, imageResult, response.originalMessage, response.userOriginalPromptEnglish, response.imagePrompt, imageStory);  // 🔧 修復：傳遞 imageStory
              logActivity('Push', '圖片回應', 'Success', 'image', 'handleSpecialResponseV2', `用戶: ${userId}(${sourceType}), 故事=${imageStory ? '有' : '無'}`);
            }
          } else {
            // 圖片生成失敗
            const errorMessage = `❌ 圖片生成失敗：${imageResult.error}`;
            if (replyToken) {
              replyMessage(replyToken, errorMessage);
            } else {
              pushTextMessageV143(targetInfo, errorMessage);
            }
            logActivity('Reply', '圖片生成失敗', 'Failure', 'text', 'handleSpecialResponseV2', `錯誤: ${imageResult.error}`);
          }
        } catch (imageError) {
          console.error('圖片生成處理錯誤:', imageError);
          const errorMessage = `❌ 圖片生成處理錯誤：${imageError.message}`;
          if (replyToken) {
            replyMessage(replyToken, errorMessage);
          } else {
            pushTextMessageV143(targetInfo, errorMessage);
          }
        }
        break;

      case 'image':
        console.log('🎨 處理圖片回應（舊格式兼容）');
        if (replyToken) {
          replyWithImage(replyToken, response, originalText, userId);
          logActivity('Reply', '圖片回應', 'Success', 'image', 'handleSpecialResponseV2', `用戶: ${userId}(${sourceType}), 配額節省=✅`);
        } else {
          // 備用：使用 Push API
          pushImageResultV143(response, originalText, originalText, targetInfo);
          logActivity('Push', '圖片回應', 'Success', 'image', 'handleSpecialResponseV2', `用戶: ${userId}(${sourceType})`);
        }
        break;

      case 'image_response_async':
        console.log('🎨 處理異步圖片回應（角色一致性）');
        // 🚨 修復：添加對 image_response_async 的處理，保留 replyToken 給圖片結果

        // 直接執行異步圖片生成（不發送安撫訊息，保留 replyToken）
        try {
          const asyncResult = executeAsyncImageGenerationV143(
            response.imagePrompt,
            response.originalMessage,
            response.userOriginalPromptEnglish,
            response.targetInfo,  // 🚨 修復：使用完整的 targetInfo（包含 replyToken）
            response.characterSeed  // 🎭 角色一致性：傳遞 SEED 參數
          );

          console.log(`🔄 異步圖片生成已觸發，replyToken 保留給圖片結果`);

        } catch (asyncError) {
          console.error('❌ 異步圖片生成觸發失敗:', asyncError);

          // 如果有 replyToken，用它發送錯誤訊息（節省配額）
          if (replyToken) {
            replyMessage(replyToken, `❌ 圖片生成啟動失敗

📝 您的描述：${response.imagePrompt}
🔧 錯誤原因：${asyncError.message}

💡 請稍後重新嘗試，或聯繫管理員檢查系統狀態`);
          } else {
            // 沒有 replyToken 才用 Push API
            try {
              pushTextMessage(userId, `❌ 圖片生成啟動失敗

📝 您的描述：${response.imagePrompt}
🔧 錯誤原因：${asyncError.message}

💡 請稍後重新嘗試，或聯繫管理員檢查系統狀態`);
            } catch (pushError) {
              console.error('❌ 錯誤通知推送也失敗:', pushError);
            }
          }
        }

        // 記錄對話（標記為異步處理）
        recordSpecialResponse(userId, originalText, `[異步圖片生成] ${response.imagePrompt}`, sourceType, userIntent);
        break;

      case 'async_image':
        console.log('🎨 處理異步圖片回應');
        // 異步圖片生成：先發送確認訊息，然後在背景生成
        if (replyToken) {
          const confirmMessage = response.confirmMessage || '🎨 正在為您生成圖片，請稍候...';
          replyMessage(replyToken, confirmMessage);
          logActivity('Reply', '異步圖片確認', 'Success', 'text', 'handleSpecialResponseV2', `用戶: ${userId}(${sourceType}), 配額節省=✅`);
        }

        // 🔧 v1.4.3 修復：傳遞完整目標信息到異步處理
        console.log(`🔧 傳遞目標信息到異步圖片生成: ${JSON.stringify(targetInfo)}`);
        generateImageAsync(response.prompt, originalText, targetInfo, response.config || {});
        break;

      default:
        console.log(`❓ 未知的特殊回應類型: ${response.type}`);
        if (replyToken) {
          replyMessage(replyToken, response.message || '處理完成');
        }
    }

  } catch (error) {
    console.error('特殊回應處理錯誤:', error);

    const errorMessage = `❌ 處理回應時發生錯誤：${error.message}

💡 請重新嘗試您的請求。`;

    if (replyToken) {
      replyMessage(replyToken, errorMessage);
      logActivity('Reply', '特殊回應錯誤', 'Failure', 'text', 'handleSpecialResponseV2', `錯誤: ${error.message}`);
    }
  }
}
