// TextProcessor_AIIntent.gs
// == AI意圖分析模組 ==
// 🔧 v1.5.2 - 從 TextProcessor_AIFirst.gs 拆分
// 📁 職責：Gemini AI 意圖分析、備用意圖生成、信心度評估

/**
 * 🤔 智能引導觸發檢查
 * 當 AI 信心度過低時觸發統一引導
 */
function shouldTriggerGuidance(aiIntent) {
  return aiIntent.confidence < 70;
}

/**
 * 🧠 核心功能：使用 Gemini AI 分析用戶意圖
 * 🔧 統一提示詞版，強化群組成員查詢識別
 */
function analyzeUserIntentWithAI(userMessage, sourceType, userId) {
  try {
    const config = getConfig();

    if (!config.geminiApiKey) {
      throw new Error('需要 Gemini API Key 才能使用 AI-First 模式');
    }



    // 🎯 使用統一提示詞系統進行意圖分析
    const aiResponse = callAIWithPrompt('INTENT_ANALYSIS', {
      userMessage: userMessage,
      sourceType: sourceType === 'group' ? '群組聊天' : sourceType === 'room' ? '聊天室' : '個人對話'
    });

    // 🎲 提取 SEED 和場景描述（角色一致性功能）
    let extractedSeed = null;
    let sceneDescription = null;

    // 檢查是否為圖片生成意圖，如果是則提取 SEED 和場景
    if (userMessage && /畫|畫圖|生成圖|圖片|image|draw|generate|創作|設計/.test(userMessage.toLowerCase())) {
      extractedSeed = extractSeedFromMessage(userMessage);
      sceneDescription = extractSceneDescription(userMessage);
    }

    // 解析 AI 回應
    try {
      const jsonMatch = aiResponse.match(/\{[\s\S]*\}/);
      if (jsonMatch) {
        const intentData = JSON.parse(jsonMatch[0]);

        // 驗證和補充數據
        intentData.original_message = userMessage;
        intentData.source_type = sourceType;
        intentData.analysis_timestamp = new Date().toISOString();

        // 🎭 添加角色一致性相關數據
        if (extractedSeed !== null) {
          intentData.extractedSeed = extractedSeed;
        }
        if (sceneDescription !== null) {
          intentData.sceneDescription = sceneDescription;
        }

        console.log(`🧠 AI 意圖分析結果: ${intentData.primary_intent} (${intentData.confidence}%)`);
        return intentData;
      } else {
        console.warn('AI 回應中未找到 JSON 格式，使用備用分析');
        return generateFallbackIntent(userMessage, sourceType);
      }
    } catch (parseError) {
      console.error('AI 回應解析失敗:', parseError);
      return generateFallbackIntent(userMessage, sourceType);
    }
    
  } catch (error) {
    console.error('AI 意圖分析錯誤:', error);
    return generateFallbackIntent(userMessage, sourceType);
  }
}

/**
 * 🛡️ 備用意圖生成（當 AI 分析失敗時）
 * 🔧 強化群組查詢檢測
 */
function generateFallbackIntent(message, sourceType) {
  // 簡單的關鍵字備用檢測
  const intent = {
    primary_intent: 'general_question',
    confidence: 30,
    key_entities: [],
    natural_language_summary: `備用分析：${message.substring(0, 50)}...`
  };

  const lowerMessage = message.toLowerCase();

  // 🎨 圖片生成檢測
  if (/畫|畫圖|生成圖|圖片|image|draw|generate|創作|設計/.test(lowerMessage)) {
    intent.primary_intent = 'image_generation';
    intent.confidence = 80;
  }
  // 🔊 TTS 檢測
  else if (/你說|你念|念出來|讀出來|語音|播放|tts|說出來/.test(lowerMessage)) {
    intent.primary_intent = 'text_to_speech';
    intent.confidence = 75;
  }
  // 🎙️ 對話音頻檢測
  else if (/跟我聊|聊聊|聊天|對話|語音聊天|語音對話|用語音|語音回應/.test(lowerMessage)) {
    intent.primary_intent = 'conversational_audio';
    intent.confidence = 75;
  }
  // 👥 群組成員查詢檢測（強化版）
  else if ((sourceType === 'group' || sourceType === 'room') && 
           (/誰|成員|member|群組|group|人員|名單|列表|who/.test(lowerMessage))) {
    intent.primary_intent = 'group_member_query';
    intent.confidence = 70;
  }
  // 📋 對話回顧檢測
  else if (/回顧|歷史|記錄|之前|以前|history|review/.test(lowerMessage)) {
    intent.primary_intent = 'conversation_review';
    intent.confidence = 65;
  }
  // 📝 筆記檢測
  else if (/記錄|筆記|note|記下|寫下|保存/.test(lowerMessage)) {
    intent.primary_intent = 'note_taking';
    intent.confidence = 65;
  }
  // 🔗 連結分享檢測
  else if (/drive\.google\.com|分享|share|連結|link/.test(lowerMessage)) {
    intent.primary_intent = 'drive_link_sharing';
    intent.confidence = 70;
  }
  // ❓ 幫助檢測
  else if (/help|幫助|功能|指令|command|怎麼|如何/.test(lowerMessage)) {
    intent.primary_intent = 'system_help';
    intent.confidence = 75;
  }
  // 📚 範例檢測
  else if (/範例|example|示範|demo|樣本/.test(lowerMessage)) {
    intent.primary_intent = 'model_examples';
    intent.confidence = 70;
  }

  console.log('🛡️ 備用意圖檢測結果:', intent);
  return intent;
}

// ===== 🎭 角色一致性功能 - SEED 提取 =====

/**
 * 🎲 從用戶訊息中提取 SEED 值
 * 使用 AI 智能識別各種 SEED 表達方式
 * @param {string} userMessage - 用戶輸入的訊息
 * @returns {number|null} 提取的 SEED 值，如果沒有找到則返回 null
 */
function extractSeedFromMessage(userMessage) {
  try {
    if (!userMessage || typeof userMessage !== 'string') {
      return null;
    }

    console.log(`🎲 提取 SEED: "${userMessage}"`);

    // 🎯 使用 AI 智能提取 SEED
    const extractPrompt = `請從以下用戶訊息中提取 SEED 值（隨機種子）。

用戶訊息：「${userMessage}」

SEED 可能的表達方式：
- SEED:123456
- 使用 SEED 123456
- 種子 123456
- seed 123456
- 隨機種子 123456

請只返回數字，如果沒有找到 SEED 則返回 "null"。
範例回應：123456 或 null`;

    const aiResponse = callGemini(extractPrompt, 'general');

    if (aiResponse && aiResponse.trim() !== 'null') {
      const seedValue = parseInt(aiResponse.trim());
      if (!isNaN(seedValue) && seedValue > 0) {
        console.log(`✅ 提取到 SEED: ${seedValue}`);
        return seedValue;
      }
    }

    console.log(`❌ 未找到有效的 SEED`);
    return null;

  } catch (error) {
    console.error('❌ 提取 SEED 失敗:', error);
    return null;
  }
}

/**
 * 🎬 從用戶訊息中提取場景描述
 * 移除 SEED 相關文字，保留純場景描述
 * @param {string} userMessage - 用戶輸入的訊息
 * @returns {string|null} 場景描述，如果沒有找到則返回 null
 */
function extractSceneDescription(userMessage) {
  try {
    if (!userMessage || typeof userMessage !== 'string') {
      return null;
    }

    console.log(`🎬 提取場景描述: "${userMessage}"`);

    // 移除常見的畫圖指令詞和 SEED 相關文字
    let sceneText = userMessage
      .replace(/^[!！]?(畫|畫圖|生成圖|圖片|image|draw|generate|創作|設計)[，,]?\s*/i, '')
      .replace(/SEED\s*[:：]\s*\d+/gi, '')
      .replace(/使用\s*SEED\s*\d+/gi, '')
      .replace(/種子\s*[:：]?\s*\d+/gi, '')
      .replace(/隨機種子\s*[:：]?\s*\d+/gi, '')
      .trim();

    // 如果還有內容，就是場景描述
    if (sceneText && sceneText.length > 0) {
      console.log(`✅ 提取到場景描述: "${sceneText}"`);
      return sceneText;
    }

    console.log(`❌ 未找到場景描述`);
    return null;

  } catch (error) {
    console.error('❌ 提取場景描述失敗:', error);
    return null;
  }
}

/**
 * 🧪 測試意圖識別功能
 */
function testIntentAnalysis_debug() {
  const testMessages = [
    '!畫圖 一個台灣白領女性 在吃麻辣鍋',
    '!畫一個台灣白領女性在吃麻辣鍋',
    '畫一隻貓',
    '生成一張圖片',
    '你好'
  ];

  console.log('🧪 開始測試意圖識別...');

  testMessages.forEach(message => {
    console.log(`\n測試訊息: "${message}"`);

    // 測試AI意圖分析
    try {
      const aiIntent = analyzeUserIntentWithAI(message, 'group', 'test_user');
      console.log(`AI分析結果: ${aiIntent.primary_intent} (${aiIntent.confidence}%)`);
    } catch (error) {
      console.log(`AI分析失敗: ${error.message}`);
    }

    // 測試備用意圖分析
    const fallbackIntent = generateFallbackIntent(message, 'group');
    console.log(`備用分析結果: ${fallbackIntent.primary_intent} (${fallbackIntent.confidence}%)`);
  });

  return '測試完成，請查看控制台LOG';
}
