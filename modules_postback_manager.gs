// PostbackDataManager.gs
// Postback 數據管理器 - 使用 ID 查詢法解決數據長度限制問題
// 🚀 v1.4.2 修復：完全解決 Flex Message 的 URI 格式問題

/**
 * 🎯 核心功能：ID 查詢法
 * 將完整的 postback 數據存儲在 Google Sheets 中，
 * 在 postback 中只傳遞簡短的 ID，避免長度限制問題
 */

/**
 * 🔧 獲取或創建工作表（GAS 兼容版本）
 */
function getOrCreateSheet(sheetName) {
  try {
    const ss = SpreadsheetApp.getActiveSpreadsheet();
    let sheet = ss.getSheetByName(sheetName);

    if (!sheet) {
      console.log(`📋 創建新工作表: ${sheetName}`);
      sheet = ss.insertSheet(sheetName);
    }

    return sheet;
  } catch (error) {
    console.error(`❌ 獲取或創建工作表失敗: ${sheetName}`, error);
    throw error;
  }
}

/**
 * 存儲 postback 數據到暫存表
 */
function storePostbackData(action, seed, model, prompt, additionalData = {}) {
  try {
    const dataId = generateUniqueId();
    const expiryTime = new Date(Date.now() + 24 * 60 * 60 * 1000);

    const sheet = getOrCreateSheet('PostbackCache');

    if (sheet.getLastRow() === 0) {
      sheet.appendRow(['ID', 'Action', 'Seed', 'Model', 'Prompt', 'AdditionalData', 'ExpiryTime', 'CreatedTime']);
    }

    sheet.appendRow([
      dataId,
      action,
      seed,
      model,
      prompt,
      JSON.stringify(additionalData),
      expiryTime,
      new Date()
    ]);

    console.log(`✅ Postback 數據已存儲，ID: ${dataId}`);
    return dataId;

  } catch (error) {
    console.error('存儲 postback 數據失敗:', error);
    return 'error_' + Date.now().toString(36);
  }
}

/**
 * 從暫存表中獲取 postback 數據
 */
function retrievePostbackData(dataId) {
  try {
    const sheet = SpreadsheetApp.getActiveSpreadsheet().getSheetByName('PostbackCache');
    if (!sheet || sheet.getLastRow() <= 1) {
      return null;
    }

    const data = sheet.getDataRange().getValues();
    const currentTime = new Date();

    for (let i = 1; i < data.length; i++) {
      if (data[i][0] === dataId) {
        const expiryTime = new Date(data[i][6]);
        if (currentTime > expiryTime) {
          sheet.deleteRow(i + 1);
          return null;
        }

        let additionalData = {};
        try {
          additionalData = JSON.parse(data[i][5] || '{}');
        } catch (parseError) {
          console.warn('解析額外數據失敗:', parseError);
        }

        return {
          action: data[i][1],
          seed: data[i][2],
          model: data[i][3],
          prompt: data[i][4],
          additionalData: additionalData,
          createdTime: data[i][7]
        };
      }
    }

    return null;

  } catch (error) {
    console.error('獲取 postback 數據失敗:', error);
    return null;
  }
}

/**
 * 生成唯一的數據 ID
 */
function generateUniqueId() {
  const timestamp = Date.now().toString(36);
  const random = Math.random().toString(36).substr(2, 6);
  return `pb_${timestamp}_${random}`;
}

/**
 * 🔧 創建安全的 postback 數據
 */
function createSafePostbackDataV2(action, seed, model, prompt, additionalData = {}) {
  try {
    const dataId = storePostbackData(action, seed, model, prompt, additionalData);
    const postbackData = `action=${action}&id=${dataId}`;

    console.log(`🔧 安全 Postback: ${postbackData} (${postbackData.length}字符)`);
    return postbackData;

  } catch (error) {
    console.error('創建安全 postback 數據失敗:', error);
    return `action=${action}&seed=${seed}&error=true`;
  }
}

/**
 * 處理 postback 數據（支援新舊兩種格式）
 * 🔧 v1.6.4 - 修正URLSearchParams兼容性問題，使用Google Apps Script兼容的解析方式
 */
function handlePostbackData(postbackData) {
  try {
    // 🔧 Google Apps Script兼容的參數解析（替代URLSearchParams）
    const params = parseQueryString(postbackData);
    const action = params.action;
    const dataId = params.id;

    // 新格式：使用 ID 查詢法
    if (dataId && !params.error) {
      const fullData = retrievePostbackData(dataId);
      if (fullData) {
        return {
          success: true,
          method: 'id_lookup',
          action: action,
          data: fullData
        };
      }
    }

    // 舊格式：直接解析參數（備用）
    const seed = params.seed;
    const model = params.model;
    const prompt = params.prompt;

    return {
      success: true,
      method: 'legacy_parsing',
      action: action,
      data: {
        action: action,
        seed: seed ? parseInt(seed) : null,
        model: model ? decodeURIComponent(model) : '',
        prompt: prompt ? decodeURIComponent(prompt) : '',
        additionalData: {}
      }
    };

  } catch (error) {
    console.error('處理 postback 數據失敗:', error);
    return {
      success: false,
      error: error.message,
      rawData: postbackData
    };
  }
}

/**
 * 🔧 Google Apps Script兼容的查詢字符串解析器
 * 替代URLSearchParams，避免"URLSearchParams is not defined"錯誤
 */
function parseQueryString(queryString) {
  const params = {};

  if (!queryString) {
    return params;
  }

  // 移除開頭的?（如果有的話）
  const cleanQuery = queryString.startsWith('?') ? queryString.substring(1) : queryString;

  // 分割參數
  const pairs = cleanQuery.split('&');

  for (let i = 0; i < pairs.length; i++) {
    const pair = pairs[i];
    const equalIndex = pair.indexOf('=');

    if (equalIndex > -1) {
      const key = pair.substring(0, equalIndex);
      const value = pair.substring(equalIndex + 1);
      params[key] = value;
    } else {
      // 沒有等號的參數，設為空字符串
      params[pair] = '';
    }
  }

  return params;
}

// 測試函數
function testPostbackIdLookup_debug() {
  console.log('🧪 === 測試 Postback ID 查詢法 ===');

  const testData = {
    action: 'show_details',
    seed: 123456,
    model: 'imagen-4.0-generate-preview-06-06',
    prompt: '測試超長提示詞內容...'
  };

  const dataId = storePostbackData(testData.action, testData.seed, testData.model, testData.prompt);
  const safePostback = createSafePostbackDataV2(testData.action, testData.seed, testData.model, testData.prompt);
  const handleResult = handlePostbackData(safePostback);

  console.log(`✅ 測試完成，數據完整性: ${handleResult.success ? '通過' : '失敗'}`);

  return {
    success: true,
    dataId: dataId,
    postbackLength: safePostback.length,
    dataIntegrity: handleResult.success
  };
}

/**
 * 🧪 測試 PostbackDataManager 修復
 */
function testPostbackDataManager_debug() {
  console.log('🧪 === 測試 PostbackDataManager 修復 ===');

  try {
    // 測試存儲數據
    console.log('\n1️⃣ 測試存儲數據...');
    const testData = {
      seed: 12345,
      model: 'gemini-2.0-flash-preview-image-generation',
      userOriginalPrompt: '測試圖片',
      aiProcessedPrompt: 'test image',
      processingTime: 3.5,
      imageUrl: 'https://example.com/test.jpg',
      imageStory: '這是一個測試故事',
      timestamp: new Date().toISOString()
    };

    const dataId = storePostbackData('story_params', 12345, 'test-model', '測試提示詞', testData);
    console.log(`  ✅ 數據存儲成功，ID: ${dataId}`);

    if (dataId.startsWith('error_')) {
      console.log(`  ❌ 存儲失敗，返回錯誤 ID: ${dataId}`);
      return { success: false, error: '存儲失敗' };
    }

    // 測試檢索數據
    console.log('\n2️⃣ 測試檢索數據...');
    const retrievedData = retrievePostbackData(dataId);

    if (retrievedData) {
      console.log(`  ✅ 數據檢索成功`);
      console.log(`  📋 檢索到的數據: ${JSON.stringify(retrievedData.additionalData)}`);
    } else {
      console.log(`  ❌ 數據檢索失敗，找不到 ID: ${dataId}`);
      return { success: false, error: '檢索失敗' };
    }

    // 測試工作表創建
    console.log('\n3️⃣ 測試工作表創建...');
    const sheet = getOrCreateSheet('PostbackCache');
    console.log(`  ✅ 工作表獲取成功，行數: ${sheet.getLastRow()}`);

    console.log('\n✅ PostbackDataManager 修復測試完成');
    return { success: true, dataId: dataId };

  } catch (error) {
    console.error(`❌ 測試失敗: ${error.message}`);
    return { success: false, error: error.message };
  }
}
