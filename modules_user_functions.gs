/**
 * == 用戶常用功能列表 ==
 * 🎯 AI意圖分析的功能提示系統
 * 📋 方便添加/刪除功能，提高AI判別準確度
 * 
 * 🔧 v1.0.0 - 初始版本
 * 📝 用途：幫助AI正確識別用戶想使用的功能
 */

/**
 * 🎯 用戶常用功能列表
 * 📋 這個列表會被傳遞給AI，幫助AI更準確地判別用戶意圖
 */
function getUserFunctionsList() {
  return {
    // 🎨 圖片相關功能
    image_functions: {
      keywords: ['畫', '圖片', '自拍', '生成圖片', '畫圖', '繪製', '創作', '設計', '插畫', '畫畫'],
      description: '圖片生成功能 - 根據描述生成AI圖片',
      intent: 'image_generation',
      examples: [
        '畫一隻可愛的貓咪',
        '生成一張風景圖片',
        '幫我畫個卡通人物',
        '創作一幅抽象畫'
      ]
    },

    // 🔊 語音相關功能
    voice_functions: {
      tts: {
        keywords: ['tts', '語音', '念出來', '讀出來', '說出來', '播放', '你說', '你念'],
        description: 'TTS文字轉語音功能',
        intent: 'text_to_speech',
        examples: [
          'tts 你好世界',
          '念出來：今天天氣很好',
          '請說出來這段文字',
          '語音播放：歡迎使用'
        ]
      },
      conversation: {
        keywords: ['聊天', '對話', '語音聊天', '語音對話', '跟我聊', '談話'],
        description: '語音對話功能 - AI語音互動',
        intent: 'conversational_audio',
        examples: [
          '跟我聊聊天氣',
          '語音對話模式',
          '我想聊天',
          '語音互動'
        ]
      }
    },

    // 📝 文字處理功能
    text_functions: {
      keywords: ['寫', '文章', '內容', '文案', '創作文字', '撰寫', '編輯'],
      description: '文字創作和處理功能',
      intent: 'text_generation',
      examples: [
        '幫我寫一篇文章',
        '創作一個故事',
        '寫個產品介紹',
        '編輯這段文字'
      ]
    },

    // 🎭 故事接龍功能
    story_functions: {
      keywords: ['故事接龍', '接龍', '續寫故事', '故事繼續'],
      description: '故事接龍功能 - 配圖故事創作',
      intent: 'story_continuation',
      examples: [
        '!故事接龍 從前有個小女孩',
        '接龍：他們來到了森林裡',
        '續寫這個故事',
        '故事繼續：突然間...'
      ]
    },

    // 🔧 系統功能
    system_functions: {
      keywords: ['幫助', 'help', '說明', '功能', '指令', '測試', 'test', '狀態', 'status'],
      description: '系統幫助和狀態功能',
      intent: 'system_help',
      examples: [
        '!help',
        '!test',
        '!status',
        '有什麼功能',
        '使用說明'
      ]
    },

    // 💬 一般聊天（預設）
    general_chat: {
      keywords: ['聊天', '問問題', '討論', '請問', '想知道'],
      description: '一般聊天對話功能',
      intent: 'general_conversation',
      examples: [
        '今天天氣如何？',
        '請問你是誰？',
        '我想了解...',
        '可以跟我聊聊嗎？'
      ]
    }
  };
}

/**
 * 🎯 AI意圖分析增強版
 * 使用功能列表來提高判別準確度
 */
function analyzeUserIntentWithFunctionsList(userMessage) {
  try {
    console.log(`🎯 [增強版] 分析用戶意圖: ${userMessage}`);
    
    // 1. 獲取功能列表
    const functionsList = getUserFunctionsList();
    
    // 2. 構建AI提示詞
    const functionsPrompt = buildFunctionsPrompt(functionsList);
    
    // 3. 構建完整的分析提示詞
    const analysisPrompt = `你是一個智能意圖分析助手。請分析用戶的請求，判斷他們想使用哪個功能。

用戶可用的功能列表：
${functionsPrompt}

用戶輸入：「${userMessage}」

請分析用戶最可能想使用的功能，並返回對應的意圖標識。

分析規則：
1. 優先匹配關鍵詞
2. 考慮上下文語境
3. 如果不確定，選擇最相近的功能
4. 如果都不匹配，返回 general_conversation

請只返回意圖標識（如：image_generation, text_to_speech, conversational_audio, general_conversation 等）`;

    // 4. 調用AI分析
    const aiResponse = callAIWithPrompt('intent_analysis', analysisPrompt);
    
    // 5. 解析AI回應
    const detectedIntent = parseIntentResponse(aiResponse);
    
    console.log(`✅ AI意圖分析結果: ${detectedIntent}`);
    
    return {
      intent: detectedIntent,
      confidence: calculateConfidence(userMessage, detectedIntent, functionsList),
      originalMessage: userMessage,
      analysisMethod: 'functions_list_enhanced'
    };
    
  } catch (error) {
    console.error('AI意圖分析失敗:', error);
    
    // 備用：使用關鍵詞匹配
    return fallbackIntentAnalysis(userMessage);
  }
}

/**
 * 🔧 構建功能列表提示詞
 */
function buildFunctionsPrompt(functionsList) {
  let prompt = '';
  
  Object.keys(functionsList).forEach(category => {
    const func = functionsList[category];
    
    if (func.intent) {
      // 單一功能
      prompt += `\n📋 ${func.description}\n`;
      prompt += `   意圖標識: ${func.intent}\n`;
      prompt += `   關鍵詞: ${func.keywords.join(', ')}\n`;
      prompt += `   範例: ${func.examples.slice(0, 2).join(', ')}\n`;
    } else {
      // 多功能分類
      Object.keys(func).forEach(subCategory => {
        if (func[subCategory].intent) {
          const subFunc = func[subCategory];
          prompt += `\n📋 ${subFunc.description}\n`;
          prompt += `   意圖標識: ${subFunc.intent}\n`;
          prompt += `   關鍵詞: ${subFunc.keywords.join(', ')}\n`;
          prompt += `   範例: ${subFunc.examples.slice(0, 2).join(', ')}\n`;
        }
      });
    }
  });
  
  return prompt;
}

/**
 * 🔍 解析AI意圖回應
 */
function parseIntentResponse(aiResponse) {
  // 提取意圖標識
  const intentPattern = /(?:image_generation|text_to_speech|conversational_audio|story_continuation|system_help|general_conversation|text_generation)/i;
  const match = aiResponse.match(intentPattern);
  
  if (match) {
    return match[0].toLowerCase();
  }
  
  // 備用：返回一般聊天
  return 'general_conversation';
}

/**
 * 📊 計算信心度
 */
function calculateConfidence(userMessage, detectedIntent, functionsList) {
  // 簡單的信心度計算邏輯
  // 可以根據關鍵詞匹配度、上下文等因素計算
  
  let confidence = 50; // 基礎信心度
  
  // 檢查關鍵詞匹配
  Object.values(functionsList).forEach(func => {
    if (func.intent === detectedIntent && func.keywords) {
      const matchedKeywords = func.keywords.filter(keyword => 
        userMessage.toLowerCase().includes(keyword.toLowerCase())
      );
      confidence += matchedKeywords.length * 20;
    }
  });
  
  return Math.min(confidence, 100);
}

/**
 * 🔄 備用意圖分析（關鍵詞匹配）
 */
function fallbackIntentAnalysis(userMessage) {
  console.log('🔄 使用備用意圖分析（關鍵詞匹配）');
  
  const functionsList = getUserFunctionsList();
  const message = userMessage.toLowerCase();
  
  // 檢查每個功能的關鍵詞
  for (const [category, func] of Object.entries(functionsList)) {
    if (func.intent && func.keywords) {
      // 單一功能
      const matched = func.keywords.some(keyword => 
        message.includes(keyword.toLowerCase())
      );
      if (matched) {
        return {
          intent: func.intent,
          confidence: 80,
          originalMessage: userMessage,
          analysisMethod: 'keyword_fallback'
        };
      }
    } else {
      // 多功能分類
      for (const [subCategory, subFunc] of Object.entries(func)) {
        if (subFunc.intent && subFunc.keywords) {
          const matched = subFunc.keywords.some(keyword => 
            message.includes(keyword.toLowerCase())
          );
          if (matched) {
            return {
              intent: subFunc.intent,
              confidence: 80,
              originalMessage: userMessage,
              analysisMethod: 'keyword_fallback'
            };
          }
        }
      }
    }
  }
  
  // 預設：一般聊天
  return {
    intent: 'general_conversation',
    confidence: 60,
    originalMessage: userMessage,
    analysisMethod: 'default_fallback'
  };
}

/**
 * 🧪 測試功能列表系統
 */
function testUserFunctionsList_debug() {
  console.log('🧪 === 測試用戶功能列表系統 ===');
  
  const testCases = [
    { input: '畫一隻可愛的貓咪', expected: 'image_generation' },
    { input: 'tts 你好世界', expected: 'text_to_speech' },
    { input: '跟我聊聊天氣', expected: 'conversational_audio' },
    { input: '!故事接龍 從前有個小女孩', expected: 'story_continuation' },
    { input: '今天天氣如何？', expected: 'general_conversation' }
  ];
  
  testCases.forEach(testCase => {
    console.log(`\n🧪 測試: ${testCase.input}`);
    const result = analyzeUserIntentWithFunctionsList(testCase.input);
    
    console.log(`   預期: ${testCase.expected}`);
    console.log(`   實際: ${result.intent}`);
    console.log(`   信心度: ${result.confidence}%`);
    console.log(`   方法: ${result.analysisMethod}`);
    
    if (result.intent === testCase.expected) {
      console.log(`   ✅ 測試通過`);
    } else {
      console.log(`   ❌ 測試失敗`);
    }
  });
}
