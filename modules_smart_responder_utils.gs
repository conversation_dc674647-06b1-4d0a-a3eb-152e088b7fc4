/*
 * 檔案: modules_smart_responder_utils.gs
 * 分類: smart_responder
 * 功能開關: 功能開關_SMART_RESPONDER
 * 描述: 智能回應工具函數
 * 依賴: [modules_group_tracker.gs, core_utils.gs]
 * 最後更新: 2025-07-06
 */

// === 智能回應工具函數 ===
// 🛠️ 支援智能回應核心系統的工具函數
// 📋 對話上下文、群組訊息、話題提取、統計管理

/**
 * 📋 獲取對話上下文
 * @param {Object} event - LINE 群組訊息事件
 * @returns {Object} 對話上下文數據
 */
function get_conversation_context(event) {
  try {
    console.log('📋 獲取對話上下文...');
    
    const groupId = event.source?.groupId || event.source?.roomId;
    const messageText = event.message?.text || '';
    const messageType = event.message?.type || 'text';
    
    if (!groupId) {
      return {
        success: false,
        reason: '不是群組訊息',
        data: null
      };
    }
    
    // 獲取發送者顯示名稱
    let senderName = event.source?.userId || 'unknown';
    try {
      const config = getConfig();
      if (config.lineChannelAccessToken) {
        const memberUrl = `https://api.line.me/v2/bot/group/${groupId}/member/${event.source.userId}`;
        const memberResponse = UrlFetchApp.fetch(memberUrl, {
          method: 'GET',
          headers: {
            'Authorization': 'Bearer ' + config.lineChannelAccessToken
          },
          muteHttpExceptions: true
        });
        
        if (memberResponse.getResponseCode() === 200) {
          const memberInfo = JSON.parse(memberResponse.getContentText());
          senderName = memberInfo.displayName || senderName;
        }
      }
    } catch (nameError) {
      console.log('無法獲取發送者名稱:', nameError.message);
    }
    
    // 獲取最近的群組對話記錄
    const recentMessages = get_recent_group_messages(groupId, 5);
    
    // 分析最近話題
    const recentTopics = extract_recent_topics(recentMessages);
    
    // 計算群組活躍度
    const lastActiveTime = recentMessages.length > 0 
      ? recentMessages[0].timestamp 
      : new Date(Date.now() - 30 * 60 * 1000); // 30分鐘前
    
    const contextData = {
      group_id: groupId,
      current_message: messageText,
      sender_name: senderName,
      message_type: messageType,
      recent_messages: recentMessages,
      recent_topics: recentTopics,
      last_active_time: lastActiveTime,
      participants_count: recentMessages.length > 0 ? 
        [...new Set(recentMessages.map(msg => msg.sender))].length : 1
    };
    
    console.log(`📋 上下文獲取成功: ${recentMessages.length} 條最近訊息, ${recentTopics.length} 個話題`);
    
    return {
      success: true,
      data: contextData
    };
    
  } catch (error) {
    console.error('❌ 獲取對話上下文失敗:', error);
    return {
      success: false,
      reason: '系統錯誤',
      data: null,
      error: error.message
    };
  }
}

/**
 * 📜 獲取最近的群組訊息
 * @param {string} groupId - 群組ID
 * @param {number} count - 獲取數量
 * @returns {Array} 最近訊息列表
 */
function get_recent_group_messages(groupId, count = 5) {
  try {
    const sheet = SpreadsheetApp.getActiveSpreadsheet().getSheetByName('群組發言記錄');
    if (!sheet) {
      console.log('群組發言記錄工作表不存在');
      return [];
    }
    
    const lastRow = sheet.getLastRow();
    if (lastRow <= 1) {
      return [];
    }
    
    // 獲取最近的記錄
    const data = sheet.getDataRange().getValues();
    const groupMessages = data.slice(1) // 跳過標題
      .filter(row => row[1] === groupId && row[5] === 'text') // 只要文字訊息
      .sort((a, b) => new Date(b[0]) - new Date(a[0])) // 按時間倒序
      .slice(0, count) // 限制數量
      .map(row => ({
        timestamp: row[0],
        sender: row[3], // 顯示名稱
        content: row[4],
        message_type: row[5]
      }));
    
    return groupMessages;
    
  } catch (error) {
    console.error('獲取最近群組訊息失敗:', error);
    return [];
  }
}

/**
 * 🏷️ 提取最近話題
 * @param {Array} messages - 訊息列表
 * @returns {Array} 話題列表
 */
function extract_recent_topics(messages) {
  try {
    if (messages.length === 0) {
      return [];
    }
    
    // 簡單的話題提取（基於關鍵詞）
    const topics = new Set();
    
    messages.forEach(msg => {
      const content = msg.content.toLowerCase();
      
      // 技術話題
      if (/ai|程式|代碼|編程|開發|系統|api|bot|機器學習|深度學習/.test(content)) {
        topics.add('技術討論');
      }
      
      // 生活話題
      if (/吃|食物|餐廳|美食|料理|飲食/.test(content)) {
        topics.add('美食生活');
      }
      
      // 工作話題
      if (/工作|會議|項目|任務|計劃|進度/.test(content)) {
        topics.add('工作相關');
      }
      
      // 學習話題
      if (/學習|教學|課程|知識|研究|書籍/.test(content)) {
        topics.add('學習分享');
      }
      
      // 閒聊話題
      if (/天氣|心情|感覺|最近|今天|昨天|明天/.test(content)) {
        topics.add('日常閒聊');
      }
    });
    
    return Array.from(topics);
    
  } catch (error) {
    console.error('提取話題失敗:', error);
    return [];
  }
}

/**
 * 🔧 啟用/禁用智能回應功能
 * @param {boolean} enabled - 是否啟用
 */
function set_smart_responder_enabled(enabled) {
  try {
    const sheet = SpreadsheetApp.getActiveSpreadsheet().getSheetByName('APIKEY');
    if (!sheet) {
      throw new Error('找不到 APIKEY 工作表');
    }
    
    // 查找功能開關行
    const data = sheet.getDataRange().getValues();
    let found = false;
    
    for (let i = 0; i < data.length; i++) {
      if (data[i][0] === '功能開關_SMART_RESPONDER') {
        sheet.getRange(i + 1, 2).setValue(enabled ? 'TRUE' : 'FALSE');
        found = true;
        break;
      }
    }
    
    if (!found) {
      // 添加新的功能開關
      sheet.appendRow(['功能開關_SMART_RESPONDER', enabled ? 'TRUE' : 'FALSE']);
    }
    
    console.log(`✅ 智能回應功能已${enabled ? '啟用' : '禁用'}`);
    
  } catch (error) {
    console.error('設定功能開關失敗:', error);
    throw error;
  }
}

/**
 * 📊 獲取智能回應統計
 * @returns {Object} 統計數據
 */
function get_smart_responder_stats() {
  try {
    const sheet = SpreadsheetApp.getActiveSpreadsheet().getSheetByName('活動日誌');
    if (!sheet) {
      return {
        total_responses: 0,
        text_responses: 0,
        voice_responses: 0,
        success_rate: 0
      };
    }
    
    const data = sheet.getDataRange().getValues();
    const smartResponses = data.filter(row => 
      row[1] === 'SmartResponse' // 通訊方式欄位
    );
    
    const totalResponses = smartResponses.length;
    const successfulResponses = smartResponses.filter(row => row[4] === 'Success').length;
    const textResponses = smartResponses.filter(row => row[5] === 'text').length;
    const voiceResponses = smartResponses.filter(row => row[5] === 'voice').length;
    
    return {
      total_responses: totalResponses,
      text_responses: textResponses,
      voice_responses: voiceResponses,
      success_rate: totalResponses > 0 ? Math.round((successfulResponses / totalResponses) * 100) : 0,
      last_response_time: smartResponses.length > 0 ? smartResponses[0][0] : null
    };
    
  } catch (error) {
    console.error('獲取統計數據失敗:', error);
    return {
      total_responses: 0,
      text_responses: 0,
      voice_responses: 0,
      success_rate: 0
    };
  }
}

/**
 * 📈 檢查智能回應功能狀態
 * @returns {Object} 功能狀態報告
 */
function check_smart_responder_status() {
  try {
    // 🔧 修復：使用更強健的功能開關檢查邏輯
    const rawValue = getConfigValue('功能開關_SMART_RESPONDER', 'APIKEY');
    const isEnabled = rawValue && (
      rawValue === 'TRUE' ||
      rawValue === true ||
      rawValue.toString().trim().toUpperCase() === 'TRUE'
    );
    const stats = get_smart_responder_stats();
    
    return {
      enabled: isEnabled,
      status: isEnabled ? '啟用' : '禁用',
      total_responses: stats.total_responses,
      success_rate: stats.success_rate,
      last_response: stats.last_response_time,
      health_check: isEnabled ? '正常' : '功能關閉'
    };
    
  } catch (error) {
    console.error('檢查功能狀態失敗:', error);
    return {
      enabled: false,
      status: '檢查失敗',
      error: error.message
    };
  }
}

/**
 * 🔄 重置智能回應統計
 */
function reset_smart_responder_stats() {
  try {
    const sheet = SpreadsheetApp.getActiveSpreadsheet().getSheetByName('活動日誌');
    if (!sheet) {
      console.log('活動日誌工作表不存在');
      return false;
    }
    
    const data = sheet.getDataRange().getValues();
    const smartResponseRows = [];
    
    // 收集需要刪除的智能回應記錄行號
    for (let i = 1; i < data.length; i++) {
      if (data[i][1] === 'SmartResponse') {
        smartResponseRows.push(i + 1); // +1 因為 getRange 是 1-based
      }
    }
    
    // 從後往前刪除（避免行號變化）
    smartResponseRows.reverse().forEach(rowNum => {
      sheet.deleteRow(rowNum);
    });
    
    console.log(`✅ 已重置智能回應統計，刪除 ${smartResponseRows.length} 條記錄`);
    return true;
    
  } catch (error) {
    console.error('重置統計失敗:', error);
    return false;
  }
}

// === 調試和測試輔助函數 ===

/**
 * 🧪 測試智能回應功能狀態
 */
function test_smart_responder_status_debug() {
  console.log('🧪 === 測試智能回應功能狀態 ===');
  
  try {
    const status = check_smart_responder_status();
    
    console.log('📊 功能狀態報告:');
    console.log(`啟用狀態: ${status.enabled ? '✅ 啟用' : '❌ 禁用'}`);
    console.log(`總回應次數: ${status.total_responses}`);
    console.log(`成功率: ${status.success_rate}%`);
    console.log(`最後回應: ${status.last_response || '無'}`);
    console.log(`健康狀態: ${status.health_check}`);
    
    // 測試統計功能
    const stats = get_smart_responder_stats();
    console.log('\n📈 詳細統計:');
    console.log(`文字回應: ${stats.text_responses}`);
    console.log(`語音回應: ${stats.voice_responses}`);
    
  } catch (error) {
    console.error('❌ 狀態測試失敗:', error);
  }
}

/**
 * 🧪 測試對話上下文獲取
 */
function test_conversation_context_debug() {
  console.log('🧪 === 測試對話上下文獲取 ===');
  
  try {
    // 模擬群組事件
    const mockEvent = {
      source: {
        type: 'group',
        groupId: 'test-group-123',
        userId: 'test-user-456'
      },
      message: {
        type: 'text',
        text: '大家覺得最近的 AI 發展如何？'
      }
    };
    
    const context = get_conversation_context(mockEvent);
    
    if (context.success) {
      console.log('✅ 上下文獲取成功');
      console.log(`群組ID: ${context.data.group_id}`);
      console.log(`當前訊息: ${context.data.current_message}`);
      console.log(`發送者: ${context.data.sender_name}`);
      console.log(`最近訊息: ${context.data.recent_messages.length} 條`);
      console.log(`話題: ${context.data.recent_topics.join(', ')}`);
      console.log(`參與者: ${context.data.participants_count} 人`);
    } else {
      console.log('❌ 上下文獲取失敗:', context.reason);
    }
    
  } catch (error) {
    console.error('❌ 上下文測試失敗:', error);
  }
}

/**
 * 🧪 測試話題提取功能
 */
function test_topic_extraction_debug() {
  console.log('🧪 === 測試話題提取功能 ===');
  
  try {
    const testMessages = [
      { content: '最近學習 Python 很有趣', sender: 'User1' },
      { content: '我想去吃火鍋', sender: 'User2' },
      { content: '明天有會議要參加', sender: 'User3' },
      { content: 'AI 發展真的很快', sender: 'User4' },
      { content: '天氣不錯，心情很好', sender: 'User5' }
    ];
    
    const topics = extract_recent_topics(testMessages);
    
    console.log('✅ 話題提取結果:');
    topics.forEach((topic, index) => {
      console.log(`${index + 1}. ${topic}`);
    });
    
    if (topics.length === 0) {
      console.log('⚠️ 沒有提取到話題');
    }
    
  } catch (error) {
    console.error('❌ 話題提取測試失敗:', error);
  }
}
