/*
 * 檔案: modules_line_webhook_core.gs
 * 分類: line
 * 功能開關: SYSTEM_HELP
 * 描述: LINE Webhook 核心入口點
 * 依賴: [core_utils.gs, core_config.gs, modules_line_message_router.gs]
 * 最後更新: 2025-07-11
 */

// == LINE Webhook 核心入口點 ==
// 專門處理 HTTP 請求入口和基本路由

// ===== HTTP 請求入口點 =====

/**
 * 🌐 處理 HTTP GET 請求（瀏覽器直接訪問）
 * 當用戶在瀏覽器中訪問 Web App URL 時會觸發此函數
 */
function doGet(e) {
  try {
    // 檢查系統狀態
    const healthCheck = systemHealthCheck();
    const config = getConfig();
    const versionInfo = getVersionInfo();
    
    // 生成 HTML 響應頁面
    const htmlContent = generateSystemStatusHTML(healthCheck, config, versionInfo);
    
    // 返回 HTML 內容
    return HtmlService.createHtmlOutput(htmlContent)
      .setTitle('LINE Bot 智能助理 - 系統狀態')
      .setXFrameOptionsMode(HtmlService.XFrameOptionsMode.ALLOWALL);
      
  } catch (error) {
    console.error('doGet 錯誤:', error);
    return generateErrorHTML(error);
  }
}

/**
 * 🔌 處理 LINE Webhook POST 請求
 * LINE 平台會發送用戶訊息到此函數
 */
function doPost(e) {
  try {
    // 初始化工作表（確保所有必要的工作表都存在）
    initializeSheets();

    // 統一記錄工作表初始化LOG
    logActivity('System', 'APIKEY工作表更新', 'Success', 'config', 'setupAPIKEYSheetSafe', '功能分類模型系統已啟用，獨立驗證規則已設定');

    // 解析 LINE Webhook 請求
    const json = JSON.parse(e.postData.contents);
    const event = json.events[0];

    if (!event) {
      console.log('沒有收到事件');
      return ContentService.createTextOutput('OK').setMimeType(ContentService.MimeType.TEXT);
    }

    // 防重複處理檢查
    const eventId = event.webhookEventId || `${event.timestamp}_${event.source?.userId || 'unknown'}`;
    const processedKey = `processed_${eventId}`;

    if (isEventAlreadyProcessed(processedKey)) {
      return ContentService.createTextOutput('OK').setMimeType(ContentService.MimeType.TEXT);
    }

    // 標記此事件為已處理
    markEventAsProcessed(processedKey);
    
    // 提取基本資訊
    const eventInfo = extractEventInfo(event);
    
    // 記錄活動日誌
    logEventActivity(event, eventInfo);
    
    // 路由到對應的處理器
    routeEventToHandler(event, eventInfo);
    
    // 返回成功響應
    return ContentService.createTextOutput('OK').setMimeType(ContentService.MimeType.TEXT);
    
  } catch (error) {
    console.error('doPost 錯誤:', error);
    logActivity('System', '系統錯誤', 'Failure', 'webhook', 'doPost', error.toString());

    // 即使出錯也要返回成功響應，避免 LINE 平台重複發送
    return ContentService.createTextOutput('ERROR').setMimeType(ContentService.MimeType.TEXT);
  }
}

// ===== 輔助函數 =====

/**
 * 生成系統狀態 HTML
 */
function generateSystemStatusHTML(healthCheck, config, versionInfo) {
  return `
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>LINE Bot 智能助理 - 系統狀態</title>
    <style>
        body { 
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 800px; 
            margin: 40px auto; 
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: #333;
            line-height: 1.6;
        }
        .container {
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 2px solid #f0f0f0;
        }
        .status-badge {
            display: inline-block;
            padding: 8px 16px;
            border-radius: 20px;
            font-weight: bold;
            margin: 5px;
        }
        .healthy { background: #d4edda; color: #155724; }
        .warning { background: #fff3cd; color: #856404; }
        .error { background: #f8d7da; color: #721c24; }
        .info-card {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin: 15px 0;
            border-left: 4px solid #667eea;
        }
        .ai-badge {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            padding: 4px 12px;
            border-radius: 15px;
            font-size: 0.9em;
        }
        .footer {
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid #e9ecef;
            text-align: center;
            color: #6c757d;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🤖 LINE Bot 智能助理</h1>
            <p>系統狀態監控面板</p>
            <div class="status-badge ${healthCheck.overall === 'healthy' ? 'healthy' : healthCheck.overall === 'warning' ? 'warning' : 'error'}">
                ${healthCheck.overall === 'healthy' ? '✅ 系統正常' : healthCheck.overall === 'warning' ? '⚠️ 系統警告' : '❌ 系統異常'}
            </div>
            <p><strong>版本：</strong> ${versionInfo.version} | <strong>更新時間：</strong> ${versionInfo.lastUpdate}</p>
        </div>
        
        <div class="info-card">
            <h3><span class="emoji">🔧</span> 核心模組</h3>
            <p><strong>Webhook：</strong> ${healthCheck.modules.core === 'healthy' ? '✅' : '❌'} 運行中</p>
            <p><strong>工作表：</strong> ${healthCheck.modules.sheets === 'healthy' ? '✅' : '❌'} 運行中</p>
            <p><strong>記憶系統：</strong> ${healthCheck.modules.memory === 'healthy' ? '✅' : '❌'} 運行中</p>
            <p><strong>🆕 Postback 處理：</strong> ✅ v1.4.0 已啟用</p>
        </div>
        
        <div class="info-card">
            <h3><span class="emoji">🤖</span> AI 功能</h3>
            <p><strong>Gemini API：</strong> ${config.geminiApiKey ? '✅ 已配置' : '❌ 未配置'}</p>
            <p><strong>一般模型：</strong> ${config.generalModel}</p>
            <p><strong>視覺模型：</strong> ${config.visionModel}</p>
            <p><strong>AI-First 模式：</strong> <span class="ai-badge">✅ 已啟用</span></p>
        </div>
        
        <div class="footer">
            <p><strong>💡 如何使用：</strong></p>
            <p>將此頁面的 URL 設定為 LINE Developers Console 中的 Webhook URL</p>
            <p>🚀 <strong>AI-First 架構</strong>：系統會自動理解您的意圖，無需記憶複雜指令</p>
            <p>🆕 <strong>v1.4.0 新功能</strong>：圖片生成後可使用互動按鈕進行收藏、詳情查看、延續故事等操作</p>
            <p style="margin-top: 20px; font-size: 0.9em;">
                <a href="https://script.google.com/home/<USER>/${ScriptApp.getScriptId()}/edit" target="_blank" style="color: #667eea; text-decoration: none;">
                    🔧 編輯此專案
                </a>
            </p>
        </div>
    </div>
</body>
</html>
  `;
}

/**
 * 生成錯誤 HTML
 */
function generateErrorHTML(error) {
  const errorHtml = `
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>LINE Bot - 系統錯誤</title>
    <style>
        body { font-family: Arial, sans-serif; max-width: 600px; margin: 50px auto; padding: 20px; }
        .error { background: #f8d7da; color: #721c24; padding: 20px; border-radius: 10px; }
    </style>
</head>
<body>
    <div class="error">
        <h2>❌ 系統錯誤</h2>
        <p><strong>錯誤訊息：</strong> ${error.message}</p>
        <p><strong>時間：</strong> ${new Date().toLocaleString('zh-TW')}</p>
        <p>請聯繫系統管理員或 
           <a href="https://script.google.com/home/<USER>/${ScriptApp.getScriptId()}/edit" target="_blank" 
              style="color: #007bff; text-decoration: none;">前往編輯器檢查</a>
        </p>
    </div>
</body>
</html>
  `;
  
  return HtmlService.createHtmlOutput(errorHtml)
    .setTitle('LINE Bot - 系統錯誤');
}

/**
 * 檢查事件是否已處理
 */
function isEventAlreadyProcessed(processedKey) {
  const alreadyProcessed = PropertiesService.getScriptProperties().getProperty(processedKey);
  if (alreadyProcessed) {
    const processedTime = parseInt(alreadyProcessed);
    const currentTime = Date.now();
    const fiveMinutes = 5 * 60 * 1000; // 5分鐘

    // 檢查是否過期（超過5分鐘）
    if (currentTime - processedTime > fiveMinutes) {
      console.log(`🕐 事件記錄已過期，重新處理: ${processedKey}`);
      // 清除過期記錄
      PropertiesService.getScriptProperties().deleteProperty(processedKey);
      return false;
    } else {
      console.log(`⚠️ 重複事件已忽略: ${processedKey}`);
      logActivity('System', '重複事件忽略', 'Success', 'duplicate', 'doPost', `重複事件: ${processedKey}`);
      return true;
    }
  }
  return false;
}

/**
 * 標記事件為已處理
 */
function markEventAsProcessed(processedKey) {
  PropertiesService.getScriptProperties().setProperty(processedKey, Date.now().toString());
}

/**
 * 提取事件基本資訊
 */
function extractEventInfo(event) {
  return {
    userId: event.source?.userId || 'unknown',
    groupId: event.source?.groupId || '',
    roomId: event.source?.roomId || '',
    replyToken: event.replyToken,
    sourceType: event.source?.groupId ? 'group' : event.source?.roomId ? 'room' : 'user',
    eventType: event.type,
    messageType: event.message?.type || event.type
  };
}

/**
 * 記錄事件活動
 */
function logEventActivity(event, eventInfo) {
  logActivity('Webhook', '收到訊息', 'Success', eventInfo.messageType, 'handleLineEvent', 
    `來自${eventInfo.sourceType}: ${eventInfo.userId}, 事件類型: ${eventInfo.eventType}, 訊息類型: ${eventInfo.messageType}, 群組ID: ${eventInfo.groupId}`);
}

/**
 * 路由事件到對應處理器
 */
function routeEventToHandler(event, eventInfo) {
  // 優先處理 postback 事件
  if (event.type === 'postback') {
    handlePostbackMessage(event.postback, eventInfo.replyToken, eventInfo.userId, eventInfo.sourceType, eventInfo.groupId, eventInfo.roomId);
    return;
  }
  
  // 群組和個人訊息路由
  if (eventInfo.groupId || eventInfo.roomId) {
    handleGroupMessage(event, eventInfo.replyToken, eventInfo.userId, eventInfo.sourceType);
  } else {
    handlePrivateMessage(event, eventInfo.replyToken, eventInfo.userId, eventInfo.sourceType);
  }
}
