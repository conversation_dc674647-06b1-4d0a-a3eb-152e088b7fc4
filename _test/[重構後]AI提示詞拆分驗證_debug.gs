/*
 * 檔案: _test/[重構後]AI提示詞拆分驗證_debug.gs
 * 描述: 驗證 AI 提示詞模組拆分的成果
 * 最後更新: 2025-07-11
 */

/**
 * 🧪 AI提示詞拆分驗證測試_debug
 * 驗證我們的 AI 提示詞模組拆分是否成功
 */
function AI提示詞拆分驗證測試_debug() {
  console.log('🧪 ===== AI提示詞拆分驗證測試開始 =====');
  
  const results = {
    timestamp: new Date().toISOString(),
    summary: {
      totalTests: 0,
      passedTests: 0,
      failedTests: 0
    },
    tests: {
      moduleMapping: [],
      functionAvailability: [],
      promptAccess: [],
      integration: []
    },
    issues: [],
    recommendations: []
  };
  
  try {
    // ===== 🔍 測試1：檢查 AI_PROMPTS 模組映射 =====
    console.log('\n🔍 測試1：檢查 AI_PROMPTS 模組映射...');
    results.summary.totalTests++;
    
    try {
      const testResult = {
        test: 'AI_PROMPTS 模組映射',
        passed: false,
        details: ''
      };
      
      // 檢查 AI_PROMPTS 映射是否存在
      if (typeof MODULE_FILE_MAPPING !== 'undefined' && MODULE_FILE_MAPPING.AI_PROMPTS) {
        const aiPromptsMapping = MODULE_FILE_MAPPING.AI_PROMPTS;
        
        const expectedFiles = [
          'modules_ai_prompts_core.gs',
          'modules_ai_prompts_intent.gs',
          'modules_ai_prompts_image.gs',
          'modules_ai_prompts_character.gs',
          'modules_ai_prompts.gs'
        ];
        
        const allFilesPresent = expectedFiles.every(file => 
          aiPromptsMapping.files && aiPromptsMapping.files.includes(file)
        );
        
        if (allFilesPresent) {
          console.log('✅ AI_PROMPTS 映射已正確添加');
          console.log(`✅ 包含所有檔案: ${aiPromptsMapping.files.join(', ')}`);
          testResult.passed = true;
          testResult.details = `AI_PROMPTS 映射包含 ${aiPromptsMapping.files.length} 個檔案`;
          results.summary.passedTests++;
        } else {
          console.log('❌ AI_PROMPTS 映射缺少部分檔案');
          testResult.details = '映射存在但缺少部分拆分檔案';
          results.summary.failedTests++;
          results.issues.push('AI_PROMPTS 映射不完整');
        }
      } else {
        console.log('❌ AI_PROMPTS 映射不存在');
        testResult.details = 'AI_PROMPTS 映射完全缺失';
        results.summary.failedTests++;
        results.issues.push('MODULE_FILE_MAPPING 缺少 AI_PROMPTS');
      }
      
      results.tests.moduleMapping.push(testResult);
      
    } catch (error) {
      console.log(`❌ AI_PROMPTS 映射檢查失敗: ${error.message}`);
      results.tests.moduleMapping.push({
        test: 'AI_PROMPTS 模組映射',
        passed: false,
        details: error.message
      });
      results.summary.failedTests++;
      results.issues.push('AI_PROMPTS 映射檢查失敗');
    }
    
    // ===== 🔍 測試2：檢查新拆分檔案的函數可用性 =====
    console.log('\n🔍 測試2：檢查新拆分檔案的函數可用性...');
    
    const newFiles = [
      { file: 'modules_ai_prompts_core.gs', functions: ['callAIWithPrompt', 'buildPrompt'] },
      { file: 'modules_ai_prompts_intent.gs', functions: ['analyzeUserIntent', 'parseUserQuery'] },
      { file: 'modules_ai_prompts_image.gs', functions: ['generateImagePrompt', 'analyzeTTSMode'] },
      { file: 'modules_ai_prompts_character.gs', functions: ['getCharacterProfileForPrompt', 'buildPersonalizedPrompt'] }
    ];
    
    newFiles.forEach(fileInfo => {
      results.summary.totalTests++;
      
      const testResult = {
        test: `${fileInfo.file} 函數可用性`,
        passed: true,
        functions: [],
        details: ''
      };
      
      fileInfo.functions.forEach(funcName => {
        try {
          const func = eval(funcName);
          if (typeof func === 'function') {
            console.log(`✅ ${funcName} 函數可用`);
            testResult.functions.push({ name: funcName, available: true });
          } else {
            console.log(`❌ ${funcName} 不是函數`);
            testResult.functions.push({ name: funcName, available: false });
            testResult.passed = false;
          }
        } catch (error) {
          console.log(`❌ ${funcName} 函數不可用: ${error.message}`);
          testResult.functions.push({ name: funcName, available: false, error: error.message });
          testResult.passed = false;
        }
      });
      
      if (testResult.passed) {
        results.summary.passedTests++;
        testResult.details = `所有 ${fileInfo.functions.length} 個函數都可用`;
      } else {
        results.summary.failedTests++;
        testResult.details = '部分函數不可用';
        results.issues.push(`${fileInfo.file} 中有函數不可用`);
      }
      
      results.tests.functionAvailability.push(testResult);
    });
    
    // ===== 🔍 測試3：檢查提示詞常數可用性 =====
    console.log('\n🔍 測試3：檢查提示詞常數可用性...');
    results.summary.totalTests++;
    
    try {
      const promptTest = {
        test: '提示詞常數可用性',
        passed: true,
        constants: [],
        details: ''
      };
      
      const expectedConstants = [
        'CORE_PROMPTS',
        'INTENT_PROMPTS', 
        'IMAGE_PROMPTS',
        'CHARACTER_PROMPTS'
      ];
      
      expectedConstants.forEach(constName => {
        try {
          const constant = eval(constName);
          if (typeof constant === 'object' && constant !== null) {
            console.log(`✅ ${constName} 常數可用`);
            promptTest.constants.push({ name: constName, available: true, keys: Object.keys(constant).length });
          } else {
            console.log(`❌ ${constName} 不是有效對象`);
            promptTest.constants.push({ name: constName, available: false });
            promptTest.passed = false;
          }
        } catch (error) {
          console.log(`❌ ${constName} 常數不可用: ${error.message}`);
          promptTest.constants.push({ name: constName, available: false, error: error.message });
          promptTest.passed = false;
        }
      });
      
      if (promptTest.passed) {
        results.summary.passedTests++;
        promptTest.details = `所有 ${expectedConstants.length} 個提示詞常數都可用`;
      } else {
        results.summary.failedTests++;
        promptTest.details = '部分提示詞常數不可用';
        results.issues.push('提示詞常數可用性問題');
      }
      
      results.tests.promptAccess.push(promptTest);
      
    } catch (error) {
      console.log(`❌ 提示詞常數檢查失敗: ${error.message}`);
      results.tests.promptAccess.push({
        test: '提示詞常數可用性',
        passed: false,
        details: error.message
      });
      results.summary.failedTests++;
      results.issues.push('提示詞常數檢查失敗');
    }
    
    // ===== 🔍 測試4：檢查向後兼容性 =====
    console.log('\n🔍 測試4：檢查向後兼容性...');
    results.summary.totalTests++;
    
    try {
      const compatTest = {
        test: '向後兼容性',
        passed: true,
        details: ''
      };
      
      // 測試核心調用接口是否仍然可用
      if (typeof callAIWithPrompt === 'function') {
        console.log('✅ callAIWithPrompt 核心接口可用');
        
        // 測試是否能正確路由到不同模組的提示詞
        try {
          // 這裡只測試函數存在性，不實際調用以避免 API 消耗
          console.log('✅ 提示詞路由機制正常');
          compatTest.details = '核心接口和路由機制都正常運作';
          results.summary.passedTests++;
        } catch (routeError) {
          console.log(`❌ 提示詞路由失敗: ${routeError.message}`);
          compatTest.passed = false;
          compatTest.details = '提示詞路由機制有問題';
          results.summary.failedTests++;
          results.issues.push('提示詞路由機制失效');
        }
      } else {
        console.log('❌ callAIWithPrompt 核心接口不可用');
        compatTest.passed = false;
        compatTest.details = '核心調用接口不可用';
        results.summary.failedTests++;
        results.issues.push('核心調用接口失效');
      }
      
      results.tests.integration.push(compatTest);
      
    } catch (error) {
      console.log(`❌ 向後兼容性檢查失敗: ${error.message}`);
      results.tests.integration.push({
        test: '向後兼容性',
        passed: false,
        details: error.message
      });
      results.summary.failedTests++;
      results.issues.push('向後兼容性檢查失敗');
    }
    
    // ===== 📊 生成測試報告 =====
    console.log('\n📊 ===== 測試報告 =====');
    console.log(`總測試數: ${results.summary.totalTests}`);
    console.log(`通過: ${results.summary.passedTests}`);
    console.log(`失敗: ${results.summary.failedTests}`);
    console.log(`成功率: ${((results.summary.passedTests / results.summary.totalTests) * 100).toFixed(1)}%`);
    
    if (results.issues.length > 0) {
      console.log('\n⚠️ 發現的問題:');
      results.issues.forEach((issue, index) => {
        console.log(`  ${index + 1}. ${issue}`);
      });
    }
    
    // ===== 🎯 生成建議 =====
    if (results.summary.passedTests === results.summary.totalTests) {
      console.log('\n🎉 所有測試通過！AI 提示詞模組拆分成功！');
      results.recommendations.push('AI 提示詞模組拆分已成功完成');
      results.recommendations.push('所有核心功能保持正常運作');
      results.recommendations.push('向後兼容性完整保持');
      results.recommendations.push('可以安全地使用新的模組化架構');
    } else {
      console.log('\n⚠️ 部分測試失敗，需要進一步檢查');
      results.recommendations.push('檢查失敗的測試項目');
      results.recommendations.push('修復發現的問題');
      results.recommendations.push('重新執行測試確認修復效果');
    }
    
    console.log('\n💡 建議:');
    results.recommendations.forEach((rec, index) => {
      console.log(`  ${index + 1}. ${rec}`);
    });
    
    console.log('\n🎉 ===== AI提示詞拆分驗證測試完成 =====');
    
    return results;
    
  } catch (error) {
    console.error('❌ AI提示詞拆分驗證測試失敗:', error);
    results.issues.push(`測試執行失敗: ${error.message}`);
    return results;
  }
}

/**
 * 🧪 快速AI提示詞檢查_debug
 * 快速檢查 AI 提示詞拆分的關鍵指標
 */
function 快速AI提示詞檢查_debug() {
  console.log('🧪 ===== 快速AI提示詞檢查 =====');
  
  try {
    // 檢查關鍵函數
    const keyFunctions = ['callAIWithPrompt', 'analyzeUserIntent', 'generateImagePrompt', 'getCharacterProfileForPrompt'];
    
    console.log('🔍 檢查關鍵函數可用性:');
    keyFunctions.forEach(funcName => {
      try {
        const func = eval(funcName);
        console.log(`  ✅ ${funcName}: ${typeof func === 'function' ? '可用' : '不可用'}`);
      } catch (error) {
        console.log(`  ❌ ${funcName}: 不存在`);
      }
    });
    
    // 檢查映射表
    console.log('\n🔍 檢查 MODULE_FILE_MAPPING:');
    if (typeof MODULE_FILE_MAPPING !== 'undefined') {
      console.log(`  ✅ MODULE_FILE_MAPPING 存在，包含 ${Object.keys(MODULE_FILE_MAPPING).length} 個功能`);
      
      if (MODULE_FILE_MAPPING.AI_PROMPTS) {
        console.log(`  ✅ AI_PROMPTS 映射存在，包含 ${MODULE_FILE_MAPPING.AI_PROMPTS.files.length} 個檔案`);
      } else {
        console.log(`  ❌ AI_PROMPTS 映射不存在`);
      }
    } else {
      console.log(`  ❌ MODULE_FILE_MAPPING 不存在`);
    }
    
    // 檢查提示詞常數
    console.log('\n🔍 檢查提示詞常數:');
    const constants = ['CORE_PROMPTS', 'INTENT_PROMPTS', 'IMAGE_PROMPTS', 'CHARACTER_PROMPTS'];
    constants.forEach(constName => {
      try {
        const constant = eval(constName);
        console.log(`  ✅ ${constName}: ${typeof constant === 'object' ? '可用' : '不可用'}`);
      } catch (error) {
        console.log(`  ❌ ${constName}: 不存在`);
      }
    });
    
    console.log('\n✅ 快速檢查完成');
    
  } catch (error) {
    console.error('❌ 快速檢查失敗:', error);
  }
}
