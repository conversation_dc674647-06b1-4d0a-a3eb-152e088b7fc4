// TextProcessor_SystemCmd.gs
// == 系統命令處理模組 ==
// 🔧 v1.5.2 - 從 TextProcessor_AIFirst.gs 拆分
// 📁 職責：系統命令預檢測、直接處理、命令常量定義

// ✅ EXPLICIT_SYSTEM_COMMANDS 常量已移至 core_helpers.gs，避免重複定義

/**
 * 🛡️ 預檢測系統命令函數
 * 在 AI 分析前先檢查明確的系統命令，確保 100% 正確執行
 * @param {string} userInput - 用戶輸入的原始文字
 * @returns {Object} 檢測結果 {isSystemCommand, commandType, confidence}
 */
function preDetectSystemCommand(userInput) {
  // 🛡️ 參數驗證
  if (!userInput || typeof userInput !== 'string') {
    return createCommandResult(false, null, 0);
  }

  // 🔄 統一處理：移除前綴驚嘆號並標準化
  const cleanInput = userInput.replace(/^[!！]/, '').trim().toLowerCase();

  // 🎪 特殊處理：故事接龍指令（支持帶參數）
  if (cleanInput.startsWith('故事接龍')) {
    return createCommandResult(true, 'story_chain', 100);
  }

  // 🔍 完全匹配檢測：查找明確的系統命令
  const commandType = EXPLICIT_SYSTEM_COMMANDS[cleanInput];
  if (commandType) {
    return createCommandResult(true, commandType, 100);
  }

  // ❌ 未檢測到系統命令
  return createCommandResult(false, null, 0);
}

/**
 * 🏭 創建命令檢測結果的工廠函數
 * @param {boolean} isSystemCommand - 是否為系統命令
 * @param {string|null} commandType - 命令類型
 * @param {number} confidence - 信心度 (0-100)
 * @returns {Object} 標準化的檢測結果
 */
function createCommandResult(isSystemCommand, commandType, confidence) {
  return {
    isSystemCommand,
    commandType,
    confidence
  };
}

/**
 * 🔧 直接系統命令處理函數
 * 繞過 AI 分析，直接執行系統命令
 */
function handleSystemCommandDirect(commandType, sourceType, replyToken, originalText = '', userId = '', targetInfo = {}) {
  let response;

  switch (commandType) {
    case 'system_test':
      response = generateTestResponse(sourceType);
      break;
    case 'system_help':
      response = generateEnhancedHelpMessage(sourceType);
      break;
    case 'system_status':
      response = generateSystemStatusResponse();
      break;
    case 'model_examples':
      response = generateModelExamplesResponse({}, sourceType);
      break;
    case 'story_chain':
      // 🎪 故事接龍處理
      console.log(`🎪 [預檢測] 處理故事接龍指令: ${originalText}`);
      return handleStoryChainCommand(originalText, replyToken, userId, sourceType, targetInfo);
    default:
      response = generateEnhancedHelpMessage(sourceType);
  }

  if (replyToken) {
    replyMessage(replyToken, response);
    logActivity('Reply', '系統命令直接處理', 'Success', 'text', 'handleSystemCommandDirect', `命令: ${commandType}`);
  }

  return response;
}
