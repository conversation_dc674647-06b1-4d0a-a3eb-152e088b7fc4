// TextProcessor_StoryChain.gs
// == 故事接龍處理模組 ==
// 🔧 v1.5.2 - 從 TextProcessor_AIFirst.gs 拆分
// 📁 職責：故事接龍、延續故事、故事狀態管理

/**
 * 🎪 故事接龍指令處理函數
 * 從現有邏輯中提取出來，供預檢測使用
 */
function handleStoryChainCommand(text, replyToken, userId, sourceType, targetInfo) {
  console.log(`🎪 [預檢測] 處理故事接龍指令: ${text}`);
  const storyContent = text.replace(/^！?故事接龍\s*/, '');
  console.log(`🎪 [預檢測] 提取的故事內容: ${storyContent}`);

  if (storyContent) {
    // 🔍 查找用戶最近的圖片記錄
    const recentImage = findRecentImageHistory(userId);

    if (recentImage) {
      console.log(`🎪 [預檢測] 找到最近圖片記錄，開始故事接龍`);

      // 🎯 統一變數命名：構建新的故事提示詞
      const userOriginalPrompt = `${recentImage.originalPrompt}, ${storyContent}`;  // 用戶原始中文輸入
      console.log(`🎪 [預檢測] 用戶原始提示詞: ${userOriginalPrompt}`);

      // 🔤 中文轉英文：故事接龍也需要英文提示詞給API
      const aiProcessedPrompt = extractImagePrompt(userOriginalPrompt);  // AI處理後的英文提示詞
      console.log(`🎪 [預檢測] AI處理後英文提示詞: ${aiProcessedPrompt}`);

      // 使用相同 SEED 生成新圖片
      const imageConfig = {
        seed: recentImage.seed
      };

      try {
        console.log(`🎨 [預檢測] 開始故事接龍圖片生成（使用英文提示詞）`);
        const imageResult = generateImageWithGemini(aiProcessedPrompt, imageConfig);  // 傳給API的是英文

        if (imageResult.success) {
          // ✅ 使用 Reply API 發送圖片結果（不占配額）
          if (replyToken) {
            console.log(`✅ [預檢測] 使用 Reply API 發送故事接龍圖片（不占配額）`);
            replyWithImage(replyToken, imageResult, userOriginalPrompt, userId);  // 顯示中文原文

            logActivity('Reply', '故事接龍成功', 'Success', 'image', 'handleStoryContinuationPreCheck', `SEED=${recentImage.seed}, 用戶=${userId}(${sourceType}), 配額節省=✅`);
            return `🎪 故事接龍圖片已生成（預檢測+Reply API）`;
          } else {
            // 備用：使用 Push API
            console.log(`📤 [預檢測] 無 replyToken，使用 Push API 發送故事接龍圖片`);
            const pushTargetInfo = {
              userId: userId,
              sourceType: sourceType,
              groupId: (targetInfo && targetInfo.groupId) || null,
              roomId: (targetInfo && targetInfo.roomId) || null
            };

            pushImageResultV143(imageResult, userOriginalPrompt, recentImage.originalPrompt, pushTargetInfo);
            logActivity('Push', '故事接龍成功', 'Success', 'image', 'handleStoryContinuationPreCheck', `SEED=${recentImage.seed}, 用戶=${userId}(${sourceType})`);
            return `🎪 故事接龍圖片已生成（預檢測+Push API）`;
          }
        } else {
          // 圖片生成失敗
          const errorMessage = `❌ 故事接龍圖片生成失敗：${imageResult.error}

💡 請重新嘗試，或使用不同的描述。`;

          if (replyToken) {
            console.log(`✅ [預檢測] 使用 Reply API 發送故事接龍錯誤（不占配額）`);
            replyMessage(replyToken, errorMessage);
          } else {
            // 備用：使用 Push API
            const pushTargetInfo = {
              userId: userId,
              sourceType: sourceType
            };
            pushTextMessageV143(pushTargetInfo, errorMessage);
          }

          return errorMessage;
        }
      } catch (syncError) {
        console.error('❌ [預檢測] 同步故事接龍失敗:', syncError);

        const errorMessage = `❌ 故事接龍過程中發生錯誤：${syncError.message}

💡 請重新使用「！故事接龍 [您的描述]」指令。`;

        if (replyToken) {
          console.log(`✅ [預檢測] 使用 Reply API 發送系統錯誤（不占配額）`);
          replyMessage(replyToken, errorMessage);
        } else {
          const pushTargetInfo = {
            userId: userId,
            sourceType: sourceType
          };
          pushTextMessageV143(pushTargetInfo, errorMessage);
        }

        return errorMessage;
      }

    } else {
      // 沒有找到最近的圖片記錄
      const noHistoryMessage = `🎪 故事接龍需要先有圖片記錄才能延續故事。

💡 請先使用「！畫圖 [描述]」生成一張圖片，然後再使用故事接龍功能。`;

      if (replyToken) {
        replyMessage(replyToken, noHistoryMessage);
      }

      return noHistoryMessage;
    }
  } else {
    // 沒有提供故事內容
    const noContentMessage = `🎪 請提供故事內容！

💡 正確格式：「！故事接龍 [您的故事描述]」
例如：「！故事接龍 然後他遇到了一隻小貓」`;

    if (replyToken) {
      replyMessage(replyToken, noContentMessage);
    }

    return noContentMessage;
  }
}

/**
 * 🆕 v1.4.0 延續故事圖片生成處理函數
 * 當用戶在延續故事模式下發送文字時，直接生成圖片
 */
function handleContinueStoryGeneration(storyState, replyToken, userId, sourceType) {
  try {
    console.log(`🎪 處理延續故事圖片生成: ${storyState.combinedPrompt}`);
    
    // 創建延續故事的意圖對象
    const storyIntent = {
      primary_intent: 'image_generation',
      confidence: 95,
      key_entities: [storyState.combinedPrompt],
      original_message: storyState.combinedPrompt,
      natural_language_summary: `用戶想要延續故事並生成圖片：${storyState.combinedPrompt}`,
      
      // 🔧 v1.4.3 修復：添加目標信息支援
      groupId: storyState.groupId || null,
      roomId: storyState.roomId || null
    };

    // 🔧 v1.4.3 修復：傳遞目標信息到圖片生成函數
    const targetInfo = {
      groupId: storyState.groupId || null,
      roomId: storyState.roomId || null
    };

    console.log(`🎪 調用圖片生成處理器，目標信息: ${JSON.stringify(targetInfo)}`);
    
    // 調用圖片生成處理器
    const result = handleAIImageGeneration(storyIntent, storyState.combinedPrompt, userId, sourceType, targetInfo);
    
    console.log(`🎪 延續故事圖片生成完成`);
    return result;
    
  } catch (error) {
    console.error('延續故事圖片生成錯誤:', error);
    
    const errorMessage = `❌ 延續故事圖片生成失敗：${error.message}

💡 請重新點擊「🎪 延續故事」按鈕，或使用：
「！繼續故事 [您的描述]」`;

    if (replyToken) {
      replyMessage(replyToken, errorMessage);
    }
    
    return errorMessage;
  }
}

/**
 * 🆕 v1.6.2 檢查用戶是否有等待中的故事延續狀態
 */
function checkStoryWaitingState(userId) {
  try {
    // 從 PostbackCache 工作表中查找等待中的故事狀態
    const sheet = SpreadsheetApp.getActiveSpreadsheet().getSheetByName('PostbackCache');
    if (!sheet || sheet.getLastRow() <= 1) {
      return null;
    }

    const data = sheet.getDataRange().getValues();
    const headers = data[0];

    // 查找用戶的等待中故事狀態
    for (let i = 1; i < data.length; i++) {
      const row = data[i];
      const rowUserId = row[headers.indexOf('userId')];
      const status = row[headers.indexOf('status')];
      const actionType = row[headers.indexOf('actionType')];

      if (rowUserId === userId && status === 'waiting' && actionType === 'story_continuation') {
        console.log(`🎬 [v1.6.2] 找到用戶 ${userId} 的等待中故事狀態`);

        return {
          previousSeed: row[headers.indexOf('previousSeed')],
          previousPrompt: row[headers.indexOf('previousPrompt')],
          timestamp: row[headers.indexOf('timestamp')],
          groupId: row[headers.indexOf('groupId')] || null,
          roomId: row[headers.indexOf('roomId')] || null,
          rowIndex: i + 1  // 用於後續清理
        };
      }
    }

    return null;

  } catch (error) {
    console.error('🎬 [v1.6.2] 檢查故事等待狀態錯誤:', error);
    return null;
  }
}

/**
 * 🆕 v1.6.2 查找用戶最近的故事狀態（用於明確指令）
 */
function findRecentStoryState(userId) {
  try {
    // 這裡可以實現查找最近生成的圖片的 SEED 和提示詞
    // 暫時返回 null，讓用戶使用按鈕方式
    console.log(`🔍 [v1.6.2] 查找用戶 ${userId} 的最近故事狀態（暫未實現）`);
    return null;
  } catch (error) {
    console.error('🔍 [v1.6.2] 查找最近故事狀態錯誤:', error);
    return null;
  }
}

/**
 * 🆕 v1.6.2 處理故事延續輸入
 */
function handleStoryInputProcessing(storyState, userInput, replyToken, userId, sourceType, targetInfo) {
  try {
    console.log(`🎬 [v1.6.2] 處理故事延續輸入: ${userInput}`);
    console.log(`🎬 [v1.6.2] 原始故事: SEED=${storyState.previousSeed}, 描述=${storyState.previousPrompt}`);

    // 構建新的故事提示詞
    const combinedPrompt = `${storyState.previousPrompt}, ${userInput}`;
    console.log(`🎬 [v1.6.2] 組合後的故事提示詞: ${combinedPrompt}`);

    // 創建故事延續的意圖對象
    const storyIntent = {
      primary_intent: 'image_generation',
      confidence: 95,
      key_entities: [combinedPrompt],
      original_message: combinedPrompt,
      natural_language_summary: `用戶延續故事：${combinedPrompt}`,
      groupId: storyState.groupId || targetInfo.groupId || null,
      roomId: storyState.roomId || targetInfo.roomId || null
    };

    // 構建目標信息
    const finalTargetInfo = {
      userId: userId,
      sourceType: sourceType,
      groupId: storyState.groupId || targetInfo.groupId || null,
      roomId: storyState.roomId || targetInfo.roomId || null
    };

    console.log(`🎬 [v1.6.2] 目標信息: ${JSON.stringify(finalTargetInfo)}`);

    // 清理等待狀態
    try {
      const sheet = SpreadsheetApp.getActiveSpreadsheet().getSheetByName('PostbackCache');
      if (sheet && storyState.rowIndex) {
        sheet.deleteRow(storyState.rowIndex);
        console.log(`🎬 [v1.6.2] 已清理用戶 ${userId} 的等待狀態`);
      }
    } catch (cleanupError) {
      console.error('🎬 [v1.6.2] 清理等待狀態失敗:', cleanupError);
    }

    // 調用圖片生成處理器
    console.log(`🎬 [v1.6.2] 調用圖片生成處理器`);
    const result = handleAIImageGeneration(storyIntent, combinedPrompt, userId, sourceType, finalTargetInfo);

    console.log(`🎬 [v1.6.2] 故事延續處理完成`);
    return result;

  } catch (error) {
    console.error('🎬 [v1.6.2] 故事延續處理錯誤:', error);

    const errorMessage = `❌ 故事延續處理失敗：${error.message}

💡 請重新點擊「🎪 延續故事」按鈕，或使用：
「！繼續故事 [您的描述]」`;

    if (replyToken) {
      replyMessage(replyToken, errorMessage);
    }

    return errorMessage;
  }
}
